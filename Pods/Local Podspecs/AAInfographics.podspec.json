{"name": "AAInfographics", "version": "9.4.0", "summary": "📈📊📱📺💻An elegant modern declarative data visualization chart framework for iOS, iPadOS and macOS. Extremely powerful, supports line, spline, area, areaspline, column, bar, pie, scatter, angular gauges, arearange, areasplinerange, columnrange, bubble, box plot, error bars, funnel, waterfall and polar chart types. 极其精美而又强大的跨平台数据可视化图表框架,支持柱状图、条形图、折线图、曲线图、折线填充图、曲线填充图、气泡图、扇形图、环形图、散点图、雷达图、混合图等各种类型的多达几十种的信息图图表,完全满足工作所需.", "homepage": "https://github.com/AAChartModel/AAChartKit-Swift", "license": "MIT", "authors": {"An An": "<EMAIL>"}, "platforms": {"ios": "10.0", "osx": "10.13"}, "source": {"git": "https://github.com/AAChartModel/AAChartKit-Swift.git", "tag": "9.4.0"}, "source_files": "AAInfographics/**/*.{swift}", "exclude_files": "AAInfographics/**/PackageBundlePathLoader.swift", "resources": "AAInfographics/AAJSFiles.bundle", "requires_arc": true, "swift_versions": "5.0", "swift_version": "5.0"}