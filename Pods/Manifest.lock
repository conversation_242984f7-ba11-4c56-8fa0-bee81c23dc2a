PODS:
  - AAInfographics (9.4.0)
  - IQKeyboardCore (1.0.5)
  - IQKeyboardManagerSwift (8.0.1):
    - IQKeyboardManagerSwift/Appearance (= 8.0.1)
    - IQKeyboardManagerSwift/Core (= 8.0.1)
    - IQKeyboardManagerSwift/IQKeyboardReturnManager (= 8.0.1)
    - IQKeyboardManagerSwift/IQKeyboardToolbarManager (= 8.0.1)
    - IQKeyboardManagerSwift/IQTextView (= 8.0.1)
    - IQKeyboardManagerSwift/Resign (= 8.0.1)
  - IQKeyboardManagerSwift/Appearance (8.0.1):
    - IQKeyboardManagerSwift/Core
  - IQKeyboardManagerSwift/Core (8.0.1):
    - IQKeyboardNotification
    - IQTextInputViewNotification
  - IQKeyboardManagerSwift/IQKeyboardReturnManager (8.0.1):
    - IQKeyboardReturnManager
  - IQKeyboardManagerSwift/IQKeyboardToolbarManager (8.0.1):
    - IQKeyboardManagerSwift/Core
    - IQKeyboardToolbarManager
  - IQKeyboardManagerSwift/IQTextView (8.0.1):
    - IQTextView
  - IQKeyboardManagerSwift/Resign (8.0.1):
    - IQKeyboardManagerSwift/Core
  - IQKeyboardNotification (1.0.3)
  - IQKeyboardReturnManager (1.0.4):
    - IQKeyboardCore (= 1.0.5)
  - IQKeyboardToolbar (1.1.1):
    - IQKeyboardCore
    - IQKeyboardToolbar/Core (= 1.1.1)
  - IQKeyboardToolbar/Core (1.1.1):
    - IQKeyboardCore
    - IQKeyboardToolbar/Placeholderable
  - IQKeyboardToolbar/Placeholderable (1.1.1):
    - IQKeyboardCore
  - IQKeyboardToolbarManager (1.1.3):
    - IQKeyboardToolbar
    - IQTextInputViewNotification
  - IQTextInputViewNotification (1.0.8):
    - IQKeyboardCore
  - IQTextView (1.0.5):
    - IQKeyboardToolbar/Placeholderable
  - SnapKit (5.7.1)

DEPENDENCIES:
  - AAInfographics (from `https://github.com/AAChartModel/AAChartKit-Swift.git`)
  - IQKeyboardManagerSwift
  - SnapKit

SPEC REPOS:
  trunk:
    - IQKeyboardCore
    - IQKeyboardManagerSwift
    - IQKeyboardNotification
    - IQKeyboardReturnManager
    - IQKeyboardToolbar
    - IQKeyboardToolbarManager
    - IQTextInputViewNotification
    - IQTextView
    - SnapKit

EXTERNAL SOURCES:
  AAInfographics:
    :git: https://github.com/AAChartModel/AAChartKit-Swift.git

CHECKOUT OPTIONS:
  AAInfographics:
    :commit: 65a8f66c8da955f486aa6d779602ee57e28c5c66
    :git: https://github.com/AAChartModel/AAChartKit-Swift.git

SPEC CHECKSUMS:
  AAInfographics: 3ec8bbc31abf0ad81b7bdbd7fa337dd7664d81ed
  IQKeyboardCore: 28c8bf3bcd8ba5aa1570b318cbc4da94b861711e
  IQKeyboardManagerSwift: 835fc9c6e4732398113406d84900ad2e8f141218
  IQKeyboardNotification: d7382c4466c5a5adef92c7452ebf861b36050088
  IQKeyboardReturnManager: 972be48528ce9e7508ab3ab15ac7efac803f17f5
  IQKeyboardToolbar: d4bdccfb78324aec2f3920659c77bb89acd33312
  IQKeyboardToolbarManager: 6c693c8478d6327a7ef2107528d29698b3514dbb
  IQTextInputViewNotification: f5e954d8881fd9808b744e49e024cc0d4bcfe572
  IQTextView: ae13b4922f22e6f027f62c557d9f4f236b19d5c7
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a

PODFILE CHECKSUM: f0bf73c2a922a54e7f063b7adfcb36809acf1413

COCOAPODS: 1.16.2
