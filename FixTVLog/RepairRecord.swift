//
//  RepairRecord.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import Foundation

struct RepairRecord: Codable {
    let id: String
    var repairDate: Date
    var tvBrand: String
    var tvModel: String
    var faultSymptoms: [String]
    var repairDescription: String
    var replacedParts: [ReplacedPart]
    var photoData: [Data]
    
    init(id: String = UUID().uuidString,
         repairDate: Date = Date(),
         tvBrand: String = "",
         tvModel: String = "",
         faultSymptoms: [String] = [],
         repairDescription: String = "",
         replacedParts: [ReplacedPart] = [],
         photoData: [Data] = []) {
        self.id = id
        self.repairDate = repairDate
        self.tvBrand = tvBrand
        self.tvModel = tvModel
        self.faultSymptoms = faultSymptoms
        self.repairDescription = repairDescription
        self.replacedParts = replacedParts
        self.photoData = photoData
    }
}

struct ReplacedPart: Codable {
    var partName: String
    var quantity: Int
    var isCustomerProvided: Bool
    
    init(partName: String = "", quantity: Int = 1, isCustomerProvided: Bool = false) {
        self.partName = partName
        self.quantity = quantity
        self.isCustomerProvided = isCustomerProvided
    }
}

// MARK: - Predefined Fault Symptoms
extension RepairRecord {
    static let predefinedFaultSymptoms = [
        "No Signal",
        "Screen Flickering", 
        "Cannot Power On",
        "Audio/Video Out of Sync",
        "No Sound",
        "Distorted Picture",
        "Remote Control Not Working",
        "HDMI Port Issues",
        "WiFi Connection Problems",
        "App Crashes",
        "Overheating",
        "Color Issues"
    ]
}
