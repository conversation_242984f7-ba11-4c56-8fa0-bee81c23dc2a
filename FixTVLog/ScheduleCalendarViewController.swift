//
//  ScheduleCalendarViewController.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import UIKit
import SnapKit

class ScheduleCalendarViewController: UIViewController {

    private let datePicker = UIDatePicker()
    private let tableView = UITableView()
    private var selectedDate = Date()
    private var schedulesForSelectedDate: [RepairSchedule] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadSchedulesForDate(selectedDate)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadSchedulesForDate(selectedDate)
    }

    private func setupUI() {
        setupGradientBackground()
        setupNavigationBar()
        setupDatePicker()
        setupTableView()
        setupConstraints()
    }

    private func setupNavigationBar() {
        title = "日历视图"
        navigationController?.navigationBar.tintColor = .white
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.white
        ]

        // Make navigation bar transparent
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true

        let closeButton = UIBarButtonItem(title: "关闭", style: .plain, target: self, action: #selector(closeTapped))
        let addButton = UIBarButtonItem(image: UIImage(systemName: "plus"), style: .plain, target: self, action: #selector(addButtonTapped))

        navigationItem.leftBarButtonItem = closeButton
        navigationItem.rightBarButtonItem = addButton
    }

    private func setupDatePicker() {
        datePicker.datePickerMode = .date
        datePicker.preferredDatePickerStyle = .compact
        datePicker.backgroundColor = .cardBackground
        datePicker.layer.cornerRadius = 12
        datePicker.date = selectedDate
        datePicker.addTarget(self, action: #selector(dateChanged), for: .valueChanged)

        view.addSubview(datePicker)
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.register(ScheduleTableViewCell.self, forCellReuseIdentifier: ScheduleTableViewCell.identifier)
        view.addSubview(tableView)
    }

    private func setupConstraints() {
        datePicker.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        tableView.snp.makeConstraints { make in
            make.top.equalTo(datePicker.snp.bottom).offset(20)
            make.leading.trailing.bottom.equalTo(view.safeAreaLayoutGuide)
        }
    }

    private func loadSchedulesForDate(_ date: Date) {
        schedulesForSelectedDate = RepairScheduleManager.shared.getSchedulesForDate(date)
        tableView.reloadData()

        // Update navigation title with selected date
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        title = formatter.string(from: date)
    }

    @objc private func closeTapped() {
        dismiss(animated: true)
    }

    @objc private func addButtonTapped() {
        let detailVC = ScheduleDetailViewController()
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }

    @objc private func dateChanged() {
        selectedDate = datePicker.date
        loadSchedulesForDate(selectedDate)
    }
}



// MARK: - UITableViewDataSource
extension ScheduleCalendarViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return schedulesForSelectedDate.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: ScheduleTableViewCell.identifier, for: indexPath) as? ScheduleTableViewCell else {
            return UITableViewCell()
        }

        cell.configure(with: schedulesForSelectedDate[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension ScheduleCalendarViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let schedule = schedulesForSelectedDate[indexPath.row]
        let detailVC = ScheduleDetailViewController(schedule: schedule)
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 140
    }
}
