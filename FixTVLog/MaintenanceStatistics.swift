//
//  MaintenanceStatistics.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import Foundation

// MARK: - Fault Type Statistics
struct FaultTypeStatistic {
    let faultType: String
    let count: Int
    let percentage: Double
}

// MARK: - Parts Usage Statistics
struct PartsUsageStatistic {
    let partName: String
    let totalUsed: Int
    let monthlyUsage: [MonthlyUsage]
}

struct MonthlyUsage {
    let month: String
    let count: Int
}

// MARK: - Brand Problem Distribution
struct BrandProblemDistribution {
    let brand: String
    let totalProblems: Int
    let problemTypes: [ProblemTypeCount]
}

struct ProblemTypeCount {
    let problemType: String
    let count: Int
    let percentage: Double
}

// MARK: - Completion Trend
struct CompletionTrend {
    let date: Date
    let completedCount: Int
    let scheduledCount: Int
}

// MARK: - Dashboard Summary
struct DashboardSummary {
    let totalRepairs: Int
    let completedRepairs: Int
    let pendingTasks: Int
    let thisWeekScheduled: Int
    let mostCommonFault: String
    let mostUsedPart: String
    let averageRepairTime: TimeInterval
    let completionRate: Double
}

// MARK: - Weekly Schedule Density
struct WeeklyScheduleDensity {
    let dayOfWeek: String
    let scheduledCount: Int
    let completedCount: Int
    let density: Double // 0.0 to 1.0
}

// MARK: - Chart Data Models
struct ChartDataPoint {
    let label: String
    let value: Double
    let color: String?

    init(label: String, value: Double, color: String? = nil) {
        self.label = label
        self.value = value
        self.color = color
    }
}

struct TrendDataPoint {
    let date: Date
    let value: Double
    let category: String?

    init(date: Date, value: Double, category: String? = nil) {
        self.date = date
        self.value = value
        self.category = category
    }
}

// MARK: - Statistics Period
enum StatisticsPeriod: String, CaseIterable {
    case week = "Week"
    case month = "Month"
    case year = "Year"

    var dateRange: DateInterval {
        let calendar = Calendar.current
        let now = Date()

        switch self {
        case .week:
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            return DateInterval(start: startOfWeek, end: now)
        case .month:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            return DateInterval(start: startOfMonth, end: now)
        case .year:
            let startOfYear = calendar.dateInterval(of: .year, for: now)?.start ?? now
            return DateInterval(start: startOfYear, end: now)
        }
    }
}

// MARK: - Chart Types
enum ChartType {
    case pie
    case column
    case line
    case bar
    case area
    case spline
}

// MARK: - Color Schemes
struct ChartColors {
    static let primary = ["#007AFF", "#34C759", "#FF9500", "#FF3B30", "#AF52DE", "#FF2D92", "#5AC8FA", "#FFCC00"]
    static let secondary = ["#5856D6", "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8"]
    static let gradient = ["#667eea", "#764ba2", "#f093fb", "#f5576c", "#4facfe", "#00f2fe"]
}

// MARK: - Extensions
extension Date {
    func startOfWeek() -> Date {
        let calendar = Calendar.current
        return calendar.dateInterval(of: .weekOfYear, for: self)?.start ?? self
    }

    func startOfMonth() -> Date {
        let calendar = Calendar.current
        return calendar.dateInterval(of: .month, for: self)?.start ?? self
    }

    func dayOfWeekString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        return formatter.string(from: self)
    }

    func monthYearString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM yyyy"
        return formatter.string(from: self)
    }
}
