//
//  ScheduleTableViewCell.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import UIKit
import SnapKit

class ScheduleTableViewCell: UITableViewCell {
    static let identifier = "ScheduleTableViewCell"

    private let containerView = UIView()
    private let statusIconImageView = UIImageView()
    private let timeLabel = UILabel()
    private let customerNameLabel = UILabel()
    private let addressLabel = UILabel()
    private let problemLabel = UILabel()
    private let statusLabel = UILabel()
    private let reminderIconImageView = UIImageView()
    private let chevronImageView = UIImageView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        // Container view with rounded corners and shadow
        containerView.backgroundColor = .cardBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        containerView.layer.shadowOpacity = 0.1
        contentView.addSubview(containerView)

        // Status Icon
        statusIconImageView.contentMode = .scaleAspectFit
        statusIconImageView.tintColor = .systemBlue
        containerView.addSubview(statusIconImageView)

        // Time Label
        timeLabel.font = .systemFont(ofSize: 16, weight: .semibold)
        timeLabel.textColor = .label
        containerView.addSubview(timeLabel)

        // Customer Name Label
        customerNameLabel.font = .systemFont(ofSize: 18, weight: .bold)
        customerNameLabel.textColor = .label
        containerView.addSubview(customerNameLabel)

        // Address Label
        addressLabel.font = .systemFont(ofSize: 14, weight: .regular)
        addressLabel.textColor = .secondaryLabel
        addressLabel.numberOfLines = 2
        containerView.addSubview(addressLabel)

        // Problem Label
        problemLabel.font = .systemFont(ofSize: 14, weight: .regular)
        problemLabel.textColor = .systemOrange
        problemLabel.numberOfLines = 2
        containerView.addSubview(problemLabel)

        // Status Label
        statusLabel.font = .systemFont(ofSize: 12, weight: .medium)
        statusLabel.textAlignment = .center
        statusLabel.layer.cornerRadius = 8
        statusLabel.layer.masksToBounds = true
        statusLabel.textColor = .white
        containerView.addSubview(statusLabel)

        // Reminder Icon
        reminderIconImageView.image = UIImage(systemName: "bell.fill")
        reminderIconImageView.contentMode = .scaleAspectFit
        reminderIconImageView.tintColor = .systemYellow
        containerView.addSubview(reminderIconImageView)

        // Chevron
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = .tertiaryLabel
        chevronImageView.contentMode = .scaleAspectFit
        containerView.addSubview(chevronImageView)

        setupConstraints()
    }

    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }

        statusIconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(24)
        }

        timeLabel.snp.makeConstraints { make in
            make.leading.equalTo(statusIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().offset(16)
        }

        statusLabel.snp.makeConstraints { make in
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.top.equalToSuperview().offset(16)
            make.width.equalTo(60)
            make.height.equalTo(24)
        }

        customerNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(timeLabel)
            make.top.equalTo(timeLabel.snp.bottom).offset(8)
            make.trailing.equalTo(statusLabel.snp.leading).offset(-8)
        }

        addressLabel.snp.makeConstraints { make in
            make.leading.equalTo(customerNameLabel)
            make.top.equalTo(customerNameLabel.snp.bottom).offset(4)
            make.trailing.equalTo(customerNameLabel)
        }

        problemLabel.snp.makeConstraints { make in
            make.leading.equalTo(customerNameLabel)
            make.top.equalTo(addressLabel.snp.bottom).offset(8)
            make.trailing.equalTo(customerNameLabel)
        }

        reminderIconImageView.snp.makeConstraints { make in
            make.leading.equalTo(customerNameLabel)
            make.top.equalTo(problemLabel.snp.bottom).offset(8)
            make.bottom.equalToSuperview().offset(-16)
            make.width.height.equalTo(16)
        }

        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }

    func configure(with schedule: RepairSchedule) {
        statusIconImageView.image = UIImage(systemName: schedule.status.icon)
        statusIconImageView.tintColor = schedule.status.color

        timeLabel.text = schedule.formattedDateTimeForCell
        customerNameLabel.text = schedule.customerName
        addressLabel.text = schedule.address
        problemLabel.text = schedule.problemDescription

        statusLabel.text = schedule.status.rawValue
        statusLabel.backgroundColor = schedule.status.color

        // Show reminder icon only for pending schedules
        reminderIconImageView.isHidden = schedule.status != .pending

        // Highlight today's schedules
        if schedule.isToday {
            containerView.layer.borderWidth = 2
            containerView.layer.borderColor = UIColor.systemBlue.cgColor
        } else {
            containerView.layer.borderWidth = 0
        }
    }
}
