//
//  FixTVLogTests.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import XCTest
@testable import FixTVLog

class FixTVLogTests: XCTestCase {
    
    override func setUpWithError() throws {
        // Clear UserDefaults for testing
        UserDefaults.standard.removeObject(forKey: "RepairRecords")
        UserDefaults.standard.removeObject(forKey: "RepairSchedules")
    }
    
    override func tearDownWithError() throws {
        // Clean up after tests
        UserDefaults.standard.removeObject(forKey: "RepairRecords")
        UserDefaults.standard.removeObject(forKey: "RepairSchedules")
    }
    
    // MARK: - RepairRecord Tests
    func testRepairRecordCreation() throws {
        let record = RepairRecord(
            tvBrand: "Samsung",
            tvModel: "UN55TU8000",
            faultSymptoms: ["No Signal"],
            repairDescription: "Test repair"
        )
        
        XCTAssertFalse(record.id.isEmpty)
        XCTAssertEqual(record.tvBrand, "Samsung")
        XCTAssertEqual(record.tvModel, "UN55TU8000")
        XCTAssertEqual(record.faultSymptoms, ["No Signal"])
        XCTAssertEqual(record.repairDescription, "Test repair")
    }
    
    func testRepairRecordManager() throws {
        let manager = RepairRecordManager.shared
        
        // Test initial state (should have default records)
        let initialRecords = manager.getRepairRecords()
        XCTAssertEqual(initialRecords.count, 3) // Default records
        
        // Test saving new record
        let newRecord = RepairRecord(
            tvBrand: "LG",
            tvModel: "OLED55C1",
            faultSymptoms: ["Screen Flickering"],
            repairDescription: "Test repair for LG TV"
        )
        
        manager.saveRepairRecord(newRecord)
        let updatedRecords = manager.getRepairRecords()
        XCTAssertEqual(updatedRecords.count, 4)
        
        // Test deleting record
        manager.deleteRepairRecord(withId: newRecord.id)
        let finalRecords = manager.getRepairRecords()
        XCTAssertEqual(finalRecords.count, 3)
    }
    
    // MARK: - RepairSchedule Tests
    func testRepairScheduleCreation() throws {
        let schedule = RepairSchedule(
            customerName: "Test Customer",
            address: "Test Address",
            contactPhone: "************",
            problemDescription: "Test problem"
        )
        
        XCTAssertFalse(schedule.id.isEmpty)
        XCTAssertEqual(schedule.customerName, "Test Customer")
        XCTAssertEqual(schedule.address, "Test Address")
        XCTAssertEqual(schedule.contactPhone, "************")
        XCTAssertEqual(schedule.problemDescription, "Test problem")
        XCTAssertEqual(schedule.status, .pending)
        XCTAssertEqual(schedule.reminderTime, .thirtyMinutes)
    }
    
    func testRepairScheduleManager() throws {
        let manager = RepairScheduleManager.shared
        
        // Test initial state (should have default schedules)
        let initialSchedules = manager.getRepairSchedules()
        XCTAssertEqual(initialSchedules.count, 3) // Default schedules
        
        // Test saving new schedule
        let newSchedule = RepairSchedule(
            customerName: "New Customer",
            scheduledDate: Date(),
            address: "New Address",
            contactPhone: "************",
            problemDescription: "New problem"
        )
        
        manager.saveRepairSchedule(newSchedule)
        let updatedSchedules = manager.getRepairSchedules()
        XCTAssertEqual(updatedSchedules.count, 4)
        
        // Test deleting schedule
        manager.deleteRepairSchedule(withId: newSchedule.id)
        let finalSchedules = manager.getRepairSchedules()
        XCTAssertEqual(finalSchedules.count, 3)
    }
    
    func testScheduleStatusEnum() throws {
        XCTAssertEqual(ScheduleStatus.pending.rawValue, "待上门")
        XCTAssertEqual(ScheduleStatus.inProgress.rawValue, "已开始")
        XCTAssertEqual(ScheduleStatus.completed.rawValue, "完成")
        XCTAssertEqual(ScheduleStatus.cancelled.rawValue, "取消")
        
        XCTAssertEqual(ScheduleStatus.pending.icon, "clock")
        XCTAssertEqual(ScheduleStatus.inProgress.icon, "wrench.and.screwdriver")
        XCTAssertEqual(ScheduleStatus.completed.icon, "checkmark.circle")
        XCTAssertEqual(ScheduleStatus.cancelled.icon, "xmark.circle")
    }
    
    func testReminderTimeEnum() throws {
        XCTAssertEqual(ReminderTime.tenMinutes.rawValue, 10)
        XCTAssertEqual(ReminderTime.thirtyMinutes.rawValue, 30)
        XCTAssertEqual(ReminderTime.oneHour.rawValue, 60)
        
        XCTAssertEqual(ReminderTime.tenMinutes.displayText, "10分钟前")
        XCTAssertEqual(ReminderTime.thirtyMinutes.displayText, "30分钟前")
        XCTAssertEqual(ReminderTime.oneHour.displayText, "1小时前")
        
        XCTAssertEqual(ReminderTime.tenMinutes.timeInterval, 600) // 10 * 60
        XCTAssertEqual(ReminderTime.thirtyMinutes.timeInterval, 1800) // 30 * 60
        XCTAssertEqual(ReminderTime.oneHour.timeInterval, 3600) // 60 * 60
    }
}
