//
//  StatisticsManager.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import Foundation

class StatisticsManager {
    static let shared = StatisticsManager()

    // Cache for expensive calculations
    private var cachedSummary: [String: DashboardSummary] = [:]
    private var cachedFaultStats: [String: [FaultTypeStatistic]] = [:]
    private var cachedPartsStats: [String: [PartsUsageStatistic]] = [:]
    private var cachedBrandStats: [String: [BrandProblemDistribution]] = [:]
    private var cachedTrends: [String: [CompletionTrend]] = [:]
    private var cacheTimestamps: [String: Date] = [:]
    private let cacheTimeout: TimeInterval = 300 // 5 minutes

    private init() {}

    // MARK: - Dashboard Summary
    func getDashboardSummary(for period: StatisticsPeriod = .all) -> DashboardSummary {
        let repairRecords = getFilteredRepairRecords(for: period)
        let schedules = getFilteredSchedules(for: period)

        let totalRepairs = repairRecords.count
        let completedRepairs = repairRecords.count // All repair records are considered completed
        let pendingTasks = schedules.filter { $0.status == .pending || $0.status == .inProgress }.count
        let thisWeekScheduled = getThisWeekScheduledCount()

        let faultStats = getFaultTypeStatistics(for: period)
        let mostCommonFault = faultStats.first?.faultType ?? "No Data"

        let partsStats = getPartsUsageStatistics(for: period)
        let mostUsedPart = partsStats.first?.partName ?? "No Data"

        let averageRepairTime: TimeInterval = 3600 // Placeholder: 1 hour average
        let completionRate = totalRepairs > 0 ? Double(completedRepairs) / Double(totalRepairs) : 0.0

        return DashboardSummary(
            totalRepairs: totalRepairs,
            completedRepairs: completedRepairs,
            pendingTasks: pendingTasks,
            thisWeekScheduled: thisWeekScheduled,
            mostCommonFault: mostCommonFault,
            mostUsedPart: mostUsedPart,
            averageRepairTime: averageRepairTime,
            completionRate: completionRate
        )
    }

    // MARK: - Fault Type Statistics
    func getFaultTypeStatistics(for period: StatisticsPeriod = .all) -> [FaultTypeStatistic] {
        let key = cacheKey(for: period, suffix: "_faults")

        // Check cache first
        if isCacheValid(for: key), let cached = cachedFaultStats[key] {
            return cached
        }

        let records = getFilteredRepairRecords(for: period)
        var faultCounts: [String: Int] = [:]

        for record in records {
            for symptom in record.faultSymptoms {
                faultCounts[symptom, default: 0] += 1
            }
        }

        let totalFaults = faultCounts.values.reduce(0, +)

        let result = faultCounts.map { (fault, count) in
            let percentage = totalFaults > 0 ? Double(count) / Double(totalFaults) * 100 : 0
            return FaultTypeStatistic(faultType: fault, count: count, percentage: percentage)
        }.sorted { $0.count > $1.count }

        // Cache the result
        cachedFaultStats[key] = result
        setCacheTimestamp(for: key)

        return result
    }

    // MARK: - Parts Usage Statistics
    func getPartsUsageStatistics(for period: StatisticsPeriod = .all) -> [PartsUsageStatistic] {
        let records = getFilteredRepairRecords(for: period)
        var partCounts: [String: Int] = [:]
        var monthlyUsage: [String: [String: Int]] = [:]

        for record in records {
            let monthKey = record.repairDate.monthYearString()

            for part in record.replacedParts {
                partCounts[part.partName, default: 0] += part.quantity

                if monthlyUsage[part.partName] == nil {
                    monthlyUsage[part.partName] = [:]
                }
                monthlyUsage[part.partName]![monthKey, default: 0] += part.quantity
            }
        }

        return partCounts.map { (partName, totalCount) in
            let monthly = monthlyUsage[partName]?.map { (month, count) in
                MonthlyUsage(month: month, count: count)
            }.sorted { $0.month < $1.month } ?? []

            return PartsUsageStatistic(partName: partName, totalUsed: totalCount, monthlyUsage: monthly)
        }.sorted { $0.totalUsed > $1.totalUsed }
    }

    // MARK: - Brand Problem Distribution
    func getBrandProblemDistribution(for period: StatisticsPeriod = .all) -> [BrandProblemDistribution] {
        let records = getFilteredRepairRecords(for: period)
        var brandProblems: [String: [String: Int]] = [:]

        for record in records {
            let brand = record.tvBrand.isEmpty ? "Unknown" : record.tvBrand

            if brandProblems[brand] == nil {
                brandProblems[brand] = [:]
            }

            for symptom in record.faultSymptoms {
                brandProblems[brand]![symptom, default: 0] += 1
            }
        }

        return brandProblems.map { (brand, problems) in
            let totalProblems = problems.values.reduce(0, +)
            let problemTypes = problems.map { (problem, count) in
                let percentage = totalProblems > 0 ? Double(count) / Double(totalProblems) * 100 : 0
                return ProblemTypeCount(problemType: problem, count: count, percentage: percentage)
            }.sorted { $0.count > $1.count }

            return BrandProblemDistribution(brand: brand, totalProblems: totalProblems, problemTypes: problemTypes)
        }.sorted { $0.totalProblems > $1.totalProblems }
    }

    // MARK: - Completion Trends
    func getCompletionTrends(for period: StatisticsPeriod = .month) -> [CompletionTrend] {
        let dateRange = period.dateRange
        let calendar = Calendar.current

        var trends: [CompletionTrend] = []
        var currentDate = dateRange.start

        // Limit the number of data points to prevent performance issues
        let maxDataPoints = 90 // Maximum 90 days of data
        let daysBetween = calendar.dateComponents([.day], from: dateRange.start, to: dateRange.end).day ?? 0

        let interval: Int
        if daysBetween > maxDataPoints {
            interval = max(1, daysBetween / maxDataPoints) // Sample every N days
        } else {
            interval = 1
        }

        var dayCount = 0
        while currentDate <= dateRange.end && trends.count < maxDataPoints {
            if dayCount % interval == 0 {
                let dayStart = calendar.startOfDay(for: currentDate)
                let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart) ?? dayStart

                let completedCount = getRepairRecordsForDateRange(start: dayStart, end: dayEnd).count
                let scheduledCount = getSchedulesForDateRange(start: dayStart, end: dayEnd).count

                trends.append(CompletionTrend(date: currentDate, completedCount: completedCount, scheduledCount: scheduledCount))
            }

            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
            dayCount += 1
        }

        return trends
    }

    // MARK: - Weekly Schedule Density
    func getWeeklyScheduleDensity() -> [WeeklyScheduleDensity] {
        let calendar = Calendar.current
        let now = Date()
        let startOfWeek = now.startOfWeek()

        var densities: [WeeklyScheduleDensity] = []

        for i in 0..<7 {
            guard let day = calendar.date(byAdding: .day, value: i, to: startOfWeek) else { continue }

            let dayStart = calendar.startOfDay(for: day)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart) ?? dayStart

            let scheduledCount = getSchedulesForDateRange(start: dayStart, end: dayEnd).count
            let completedCount = getRepairRecordsForDateRange(start: dayStart, end: dayEnd).count

            let maxDensity = max(scheduledCount, 10) // Normalize to max 10 for density calculation
            let density = Double(scheduledCount) / Double(maxDensity)

            densities.append(WeeklyScheduleDensity(
                dayOfWeek: day.dayOfWeekString(),
                scheduledCount: scheduledCount,
                completedCount: completedCount,
                density: min(density, 1.0)
            ))
        }

        return densities
    }

    // MARK: - Helper Methods
    private func getFilteredRepairRecords(for period: StatisticsPeriod) -> [RepairRecord] {
        let allRecords = RepairRecordManager.shared.getRepairRecords()
        let dateRange = period.dateRange

        return allRecords.filter { record in
            dateRange.contains(record.repairDate)
        }
    }

    private func getFilteredSchedules(for period: StatisticsPeriod) -> [RepairSchedule] {
        let allSchedules = RepairScheduleManager.shared.getRepairSchedules()
        let dateRange = period.dateRange

        return allSchedules.filter { schedule in
            dateRange.contains(schedule.scheduledDate)
        }
    }

    private func getRepairRecordsForDateRange(start: Date, end: Date) -> [RepairRecord] {
        let allRecords = RepairRecordManager.shared.getRepairRecords()
        return allRecords.filter { record in
            record.repairDate >= start && record.repairDate < end
        }
    }

    private func getSchedulesForDateRange(start: Date, end: Date) -> [RepairSchedule] {
        let allSchedules = RepairScheduleManager.shared.getRepairSchedules()
        return allSchedules.filter { schedule in
            schedule.scheduledDate >= start && schedule.scheduledDate < end
        }
    }

    private func getThisWeekScheduledCount() -> Int {
        let calendar = Calendar.current
        let now = Date()
        let startOfWeek = now.startOfWeek()
        let endOfWeek = calendar.date(byAdding: .weekOfYear, value: 1, to: startOfWeek) ?? now

        return getSchedulesForDateRange(start: startOfWeek, end: endOfWeek).count
    }

    // MARK: - Cache Management
    private func cacheKey(for period: StatisticsPeriod, suffix: String = "") -> String {
        return "\(period.rawValue)\(suffix)"
    }

    private func isCacheValid(for key: String) -> Bool {
        guard let timestamp = cacheTimestamps[key] else { return false }
        return Date().timeIntervalSince(timestamp) < cacheTimeout
    }

    private func setCacheTimestamp(for key: String) {
        cacheTimestamps[key] = Date()
    }

    func clearCache() {
        cachedSummary.removeAll()
        cachedFaultStats.removeAll()
        cachedPartsStats.removeAll()
        cachedBrandStats.removeAll()
        cachedTrends.removeAll()
        cacheTimestamps.removeAll()
    }
}
