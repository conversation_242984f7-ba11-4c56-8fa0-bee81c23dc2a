//
//  ScheduleDetailViewController.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import UIKit
import SnapKit

class ScheduleDetailViewController: UIViewController {

    private let scrollView = UIScrollView()
    private let contentView = UIView()

    // Form fields
    private let customerNameTextField = UITextField()
    private let dateTimePicker = UIDatePicker()
    private let addressTextView = UITextView()
    private let contactPhoneTextField = UITextField()
    private let problemDescriptionTextView = UITextView()
    private let statusSegmentedControl = UISegmentedControl(items: ScheduleStatus.allCases.map { $0.rawValue })
    private let reminderSegmentedControl = UISegmentedControl(items: ReminderTime.allCases.map { $0.displayText })

    // Data
    private var repairSchedule: RepairSchedule
    private var isEditingExistingSchedule: Bool

    // MARK: - Initialization
    init(schedule: RepairSchedule? = nil) {
        self.isEditingExistingSchedule = schedule != nil
        self.repairSchedule = schedule ?? RepairSchedule()
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        loadScheduleData()
    }

    private func setupUI() {
        setupGradientBackground()
        setupScrollView()
        setupFormFields()
        setupConstraints()
    }

    private func setupNavigationBar() {
        title = isEditingExistingSchedule ? "编辑日程" : "新建日程"
        navigationController?.navigationBar.tintColor = .white
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.white
        ]

        // Make navigation bar transparent
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true

        let cancelButton = UIBarButtonItem(title: "取消", style: .plain, target: self, action: #selector(cancelTapped))
        let saveButton = UIBarButtonItem(title: "保存", style: .done, target: self, action: #selector(saveTapped))

        navigationItem.leftBarButtonItem = cancelButton
        navigationItem.rightBarButtonItem = saveButton
    }

    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        scrollView.keyboardDismissMode = .onDrag
    }

    private func setupFormFields() {
        // Customer Name TextField
        customerNameTextField.placeholder = "客户姓名"
        customerNameTextField.borderStyle = .roundedRect
        customerNameTextField.backgroundColor = .cardBackground
        contentView.addSubview(customerNameTextField)

        // Date Time Picker
        dateTimePicker.datePickerMode = .dateAndTime
        dateTimePicker.preferredDatePickerStyle = .compact
        dateTimePicker.backgroundColor = .cardBackground
        dateTimePicker.layer.cornerRadius = 8
        contentView.addSubview(dateTimePicker)

        // Address TextView
        addressTextView.font = .systemFont(ofSize: 16)
        addressTextView.backgroundColor = .cardBackground
        addressTextView.layer.cornerRadius = 8
        addressTextView.layer.borderColor = UIColor.separator.cgColor
        addressTextView.layer.borderWidth = 0.5
        addressTextView.text = "请输入详细地址..."
        addressTextView.textColor = .placeholderText
        addressTextView.delegate = self
        contentView.addSubview(addressTextView)

        // Contact Phone TextField
        contactPhoneTextField.placeholder = "联系电话"
        contactPhoneTextField.borderStyle = .roundedRect
        contactPhoneTextField.backgroundColor = .cardBackground
        contactPhoneTextField.keyboardType = .phonePad
        contentView.addSubview(contactPhoneTextField)

        // Problem Description TextView
        problemDescriptionTextView.font = .systemFont(ofSize: 16)
        problemDescriptionTextView.backgroundColor = .cardBackground
        problemDescriptionTextView.layer.cornerRadius = 8
        problemDescriptionTextView.layer.borderColor = UIColor.separator.cgColor
        problemDescriptionTextView.layer.borderWidth = 0.5
        problemDescriptionTextView.text = "请描述电视故障问题..."
        problemDescriptionTextView.textColor = .placeholderText
        problemDescriptionTextView.delegate = self
        contentView.addSubview(problemDescriptionTextView)

        // Status Segmented Control
        statusSegmentedControl.backgroundColor = .cardBackground
        statusSegmentedControl.selectedSegmentTintColor = .systemBlue
        statusSegmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        contentView.addSubview(statusSegmentedControl)

        // Reminder Segmented Control
        reminderSegmentedControl.backgroundColor = .cardBackground
        reminderSegmentedControl.selectedSegmentTintColor = .systemOrange
        reminderSegmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        contentView.addSubview(reminderSegmentedControl)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        let customerLabel = createSectionLabel(text: "客户姓名")
        let dateTimeLabel = createSectionLabel(text: "预约时间")
        let addressLabel = createSectionLabel(text: "服务地址")
        let contactLabel = createSectionLabel(text: "联系电话")
        let problemLabel = createSectionLabel(text: "问题描述")
        let statusLabel = createSectionLabel(text: "任务状态")
        let reminderLabel = createSectionLabel(text: "提醒时间")

        contentView.addSubview(customerLabel)
        contentView.addSubview(dateTimeLabel)
        contentView.addSubview(addressLabel)
        contentView.addSubview(contactLabel)
        contentView.addSubview(problemLabel)
        contentView.addSubview(statusLabel)
        contentView.addSubview(reminderLabel)

        // Layout constraints
        customerLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        customerNameTextField.snp.makeConstraints { make in
            make.top.equalTo(customerLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        dateTimeLabel.snp.makeConstraints { make in
            make.top.equalTo(customerNameTextField.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        dateTimePicker.snp.makeConstraints { make in
            make.top.equalTo(dateTimeLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        addressLabel.snp.makeConstraints { make in
            make.top.equalTo(dateTimePicker.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        addressTextView.snp.makeConstraints { make in
            make.top.equalTo(addressLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(80)
        }

        contactLabel.snp.makeConstraints { make in
            make.top.equalTo(addressTextView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        contactPhoneTextField.snp.makeConstraints { make in
            make.top.equalTo(contactLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        problemLabel.snp.makeConstraints { make in
            make.top.equalTo(contactPhoneTextField.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        problemDescriptionTextView.snp.makeConstraints { make in
            make.top.equalTo(problemLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(80)
        }

        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(problemDescriptionTextView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        statusSegmentedControl.snp.makeConstraints { make in
            make.top.equalTo(statusLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }

        reminderLabel.snp.makeConstraints { make in
            make.top.equalTo(statusSegmentedControl.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        reminderSegmentedControl.snp.makeConstraints { make in
            make.top.equalTo(reminderLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(32)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // MARK: - Helper Methods
    private func createSectionLabel(text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .white
        return label
    }

    private func loadScheduleData() {
        if isEditingExistingSchedule {
            customerNameTextField.text = repairSchedule.customerName
            dateTimePicker.date = repairSchedule.scheduledDate

            if !repairSchedule.address.isEmpty {
                addressTextView.text = repairSchedule.address
                addressTextView.textColor = .label
            }

            contactPhoneTextField.text = repairSchedule.contactPhone

            if !repairSchedule.problemDescription.isEmpty {
                problemDescriptionTextView.text = repairSchedule.problemDescription
                problemDescriptionTextView.textColor = .label
            }

            statusSegmentedControl.selectedSegmentIndex = ScheduleStatus.allCases.firstIndex(of: repairSchedule.status) ?? 0
            reminderSegmentedControl.selectedSegmentIndex = ReminderTime.allCases.firstIndex(of: repairSchedule.reminderTime) ?? 1
        } else {
            // Set default values for new schedule
            dateTimePicker.date = Date().addingTimeInterval(3600) // 1 hour from now
            statusSegmentedControl.selectedSegmentIndex = 0 // pending
            reminderSegmentedControl.selectedSegmentIndex = 1 // 30 minutes
        }
    }

    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }

    @objc private func saveTapped() {
        let customerName = customerNameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let address = addressTextView.text == "请输入详细地址..." ? "" : (addressTextView.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? "")
        let contactPhone = contactPhoneTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let problemDescription = problemDescriptionTextView.text == "请描述电视故障问题..." ? "" : (problemDescriptionTextView.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? "")

        guard !customerName.isEmpty else {
            showAlert(title: "信息不完整", message: "请输入客户姓名。")
            return
        }

        guard !address.isEmpty else {
            showAlert(title: "信息不完整", message: "请输入服务地址。")
            return
        }

        guard !contactPhone.isEmpty else {
            showAlert(title: "信息不完整", message: "请输入联系电话。")
            return
        }

        // Update schedule with form data
        repairSchedule.customerName = customerName
        repairSchedule.scheduledDate = dateTimePicker.date
        repairSchedule.address = address
        repairSchedule.contactPhone = contactPhone
        repairSchedule.problemDescription = problemDescription
        repairSchedule.status = ScheduleStatus.allCases[statusSegmentedControl.selectedSegmentIndex]
        repairSchedule.reminderTime = ReminderTime.allCases[reminderSegmentedControl.selectedSegmentIndex]

        RepairScheduleManager.shared.saveRepairSchedule(repairSchedule)
        dismiss(animated: true)
    }
}

// MARK: - UITextViewDelegate
extension ScheduleDetailViewController: UITextViewDelegate {
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == .placeholderText {
            textView.text = ""
            textView.textColor = .label
        }
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            if textView == addressTextView {
                textView.text = "请输入详细地址..."
            } else if textView == problemDescriptionTextView {
                textView.text = "请描述电视故障问题..."
            }
            textView.textColor = .placeholderText
        }
    }
}
