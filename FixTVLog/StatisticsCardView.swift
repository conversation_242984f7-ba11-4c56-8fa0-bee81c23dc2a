//
//  StatisticsCardView.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import UIKit
import SnapKit

class StatisticsCardView: UIView {
    
    private let iconImageView = UIImageView()
    private let titleLabel = UILabel()
    private let valueLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let trendImageView = UIImageView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        backgroundColor = .cardBackground
        layer.cornerRadius = 12
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 4
        layer.shadowOpacity = 0.1
        
        // Icon
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = .systemBlue
        addSubview(iconImageView)
        
        // Title
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.numberOfLines = 1
        addSubview(titleLabel)
        
        // Value
        valueLabel.font = .systemFont(ofSize: 24, weight: .bold)
        valueLabel.textColor = .label
        valueLabel.numberOfLines = 1
        addSubview(valueLabel)
        
        // Subtitle
        subtitleLabel.font = .systemFont(ofSize: 12, weight: .regular)
        subtitleLabel.textColor = .tertiaryLabel
        subtitleLabel.numberOfLines = 2
        addSubview(subtitleLabel)
        
        // Trend
        trendImageView.contentMode = .scaleAspectFit
        trendImageView.isHidden = true
        addSubview(trendImageView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        iconImageView.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
            make.width.height.equalTo(24)
        }
        
        trendImageView.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(16)
            make.width.height.equalTo(20)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Configuration Methods
    
    func configure(icon: String, title: String, value: String, subtitle: String? = nil, trend: TrendDirection? = nil) {
        iconImageView.image = UIImage(systemName: icon)
        titleLabel.text = title
        valueLabel.text = value
        subtitleLabel.text = subtitle
        
        if let trend = trend {
            configureTrend(trend)
        } else {
            trendImageView.isHidden = true
        }
    }
    
    func configureWithColor(icon: String, title: String, value: String, subtitle: String? = nil, color: UIColor, trend: TrendDirection? = nil) {
        configure(icon: icon, title: title, value: value, subtitle: subtitle, trend: trend)
        iconImageView.tintColor = color
        valueLabel.textColor = color
    }
    
    private func configureTrend(_ trend: TrendDirection) {
        trendImageView.isHidden = false
        
        switch trend {
        case .up:
            trendImageView.image = UIImage(systemName: "arrow.up.circle.fill")
            trendImageView.tintColor = .systemGreen
        case .down:
            trendImageView.image = UIImage(systemName: "arrow.down.circle.fill")
            trendImageView.tintColor = .systemRed
        case .stable:
            trendImageView.image = UIImage(systemName: "minus.circle.fill")
            trendImageView.tintColor = .systemGray
        }
    }
}

// MARK: - Trend Direction
enum TrendDirection {
    case up
    case down
    case stable
}

// MARK: - Quick Configuration Extensions
extension StatisticsCardView {
    
    static func totalRepairsCard(count: Int) -> StatisticsCardView {
        let card = StatisticsCardView()
        card.configure(
            icon: "wrench.and.screwdriver.fill",
            title: "Total Repairs",
            value: "\(count)",
            subtitle: "Completed repairs"
        )
        return card
    }
    
    static func pendingTasksCard(count: Int) -> StatisticsCardView {
        let card = StatisticsCardView()
        card.configureWithColor(
            icon: "clock.fill",
            title: "Pending Tasks",
            value: "\(count)",
            subtitle: "Awaiting completion",
            color: .systemOrange
        )
        return card
    }
    
    static func completionRateCard(rate: Double) -> StatisticsCardView {
        let card = StatisticsCardView()
        let percentage = Int(rate * 100)
        let trend: TrendDirection = rate >= 0.8 ? .up : (rate >= 0.6 ? .stable : .down)
        
        card.configureWithColor(
            icon: "chart.line.uptrend.xyaxis",
            title: "Completion Rate",
            value: "\(percentage)%",
            subtitle: "Success rate",
            color: .systemGreen,
            trend: trend
        )
        return card
    }
    
    static func weeklyScheduleCard(count: Int) -> StatisticsCardView {
        let card = StatisticsCardView()
        card.configureWithColor(
            icon: "calendar.badge.clock",
            title: "This Week",
            value: "\(count)",
            subtitle: "Scheduled repairs",
            color: .systemBlue
        )
        return card
    }
    
    static func mostCommonFaultCard(fault: String, count: Int) -> StatisticsCardView {
        let card = StatisticsCardView()
        card.configureWithColor(
            icon: "exclamationmark.triangle.fill",
            title: "Most Common Fault",
            value: fault,
            subtitle: "\(count) occurrences",
            color: .systemRed
        )
        return card
    }
    
    static func mostUsedPartCard(part: String, count: Int) -> StatisticsCardView {
        let card = StatisticsCardView()
        card.configureWithColor(
            icon: "gearshape.fill",
            title: "Most Used Part",
            value: part,
            subtitle: "\(count) times used",
            color: .systemPurple
        )
        return card
    }
}
