//
//  ChartCardView.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import UIKit
import SnapKit
import AAInfographics

class ChartCardView: UIView {

    private let titleLabel = UILabel()
    private let chartView = AAChartView()
    private let loadingIndicator = UIActivityIndicatorView(style: .medium)

    var title: String = "" {
        didSet {
            titleLabel.text = title
        }
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .cardBackground
        layer.cornerRadius = 12
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 4
        layer.shadowOpacity = 0.1

        // Title Label
        titleLabel.font = .systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .label
        titleLabel.numberOfLines = 1
        addSubview(titleLabel)

        // Chart View
        chartView.backgroundColor = .clear
        chartView.layer.cornerRadius = 8
        addSubview(chartView)

        // Loading Indicator
        loadingIndicator.hidesWhenStopped = true
        addSubview(loadingIndicator)

        setupConstraints()
    }

    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        chartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.greaterThanOrEqualTo(200)
        }

        loadingIndicator.snp.makeConstraints { make in
            make.center.equalTo(chartView)
        }
    }

    // MARK: - Chart Configuration Methods

    func configurePieChart(data: [ChartDataPoint], title: String) {
        self.title = title

        let chartModel = AAChartModel()
            .chartType(.pie)
            .backgroundColor("#00000000")
            .dataLabelsEnabled(true)
            .legendEnabled(true)
            .series([
                AASeriesElement()
                    .name("Count")
                    .data(data.map { AADataElement().name($0.label).y(Float($0.value)) })
                    .colorByPoint(true)
            ])

        chartView.aa_drawChartWithChartModel(chartModel)
        hideLoading()
    }

    func configureColumnChart(data: [ChartDataPoint], title: String, yAxisTitle: String = "Count") {
        self.title = title

        let chartModel = AAChartModel()
            .chartType(.column)
            .backgroundColor("#00000000")
            .dataLabelsEnabled(true)
            .legendEnabled(false)
            .categories(data.map { $0.label })
            .yAxisTitle(yAxisTitle)
            .series([
                AASeriesElement()
                    .name(yAxisTitle)
                    .data(data.map { Float($0.value) })
                    .color("#007AFF")
            ])

        chartView.aa_drawChartWithChartModel(chartModel)
        hideLoading()
    }

    func configureLineChart(data: [TrendDataPoint], title: String, yAxisTitle: String = "Count") {
        self.title = title

        let formatter = DateFormatter()
        formatter.dateFormat = "MMM dd"

        let categories = data.map { formatter.string(from: $0.date) }
        let values = data.map { Float($0.value) }

        let chartModel = AAChartModel()
            .chartType(.spline)
            .backgroundColor("#00000000")
            .dataLabelsEnabled(false)
            .legendEnabled(true)
            .categories(categories)
            .yAxisTitle(yAxisTitle)
            .markerRadius(4)
            .series([
                AASeriesElement()
                    .name(yAxisTitle)
                    .data(values)
                    .color("#007AFF")
                    .lineWidth(3)
            ])

        chartView.aa_drawChartWithChartModel(chartModel)
        hideLoading()
    }

    func configureMultiLineChart(completedData: [TrendDataPoint], scheduledData: [TrendDataPoint], title: String) {
        self.title = title

        let formatter = DateFormatter()
        formatter.dateFormat = "MMM dd"

        let categories = completedData.map { formatter.string(from: $0.date) }
        let completedValues = completedData.map { Float($0.value) }
        let scheduledValues = scheduledData.map { Float($0.value) }

        let chartModel = AAChartModel()
            .chartType(.spline)
            .backgroundColor("#00000000")
            .dataLabelsEnabled(false)
            .legendEnabled(true)
            .categories(categories)
            .yAxisTitle("Count")
            .markerRadius(4)
            .series([
                AASeriesElement()
                    .name("Completed")
                    .data(completedValues)
                    .color("#34C759")
                    .lineWidth(3),
                AASeriesElement()
                    .name("Scheduled")
                    .data(scheduledValues)
                    .color("#007AFF")
                    .lineWidth(3)
            ])

        chartView.aa_drawChartWithChartModel(chartModel)
        hideLoading()
    }

    func configureBarChart(data: [ChartDataPoint], title: String, yAxisTitle: String = "Count") {
        self.title = title

        let chartModel = AAChartModel()
            .chartType(.bar)
            .backgroundColor("#00000000")
            .dataLabelsEnabled(true)
            .legendEnabled(false)
            .categories(data.map { $0.label })
            .yAxisTitle(yAxisTitle)
            .series([
                AASeriesElement()
                    .name(yAxisTitle)
                    .data(data.map { Float($0.value) })
                    .color("#FF9500")
            ])

        chartView.aa_drawChartWithChartModel(chartModel)
        hideLoading()
    }

    func configureAreaChart(data: [TrendDataPoint], title: String, yAxisTitle: String = "Density") {
        self.title = title
        loadingIndicator.startAnimating()

        let categories = data.map { $0.category ?? "" }
        let values = data.map { Float($0.value) }

        let chartModel = AAChartModel()
            .chartType(.areaspline)
            .backgroundColor("#00000000")
            .dataLabelsEnabled(true)
            .legendEnabled(false)
            .categories(categories)
            .yAxisTitle(yAxisTitle)
            .series([
                AASeriesElement()
                    .name(yAxisTitle)
                    .data(values)
                    .color("#AF52DE")
                    .fillOpacity(0.3)
            ])

        chartView.aa_drawChartWithChartModel(chartModel)
        loadingIndicator.stopAnimating()
    }

    // MARK: - Loading State
    func showLoading() {
        loadingIndicator.startAnimating()
        chartView.alpha = 0.3
    }

    func hideLoading() {
        loadingIndicator.stopAnimating()
        chartView.alpha = 1.0
    }
}
