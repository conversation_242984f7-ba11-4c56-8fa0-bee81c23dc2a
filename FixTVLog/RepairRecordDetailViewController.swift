//
//  RepairRecordDetailViewController.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import UIKit
import SnapKit

class RepairRecordDetailViewController: UIViewController {

    private let scrollView = UIScrollView()
    private let contentView = UIView()

    // Form fields
    private let datePicker = UIDatePicker()
    private let brandTextField = UITextField()
    private let modelTextField = UITextField()
    private let symptomsStackView = UIStackView()
    private let customSymptomTextField = UITextField()
    private let repairDescriptionTextView = UITextView()
    private let partsStackView = UIStackView()
    private let photosCollectionView: UICollectionView

    // Data
    private var repairRecord: RepairRecord
    private var selectedSymptoms: Set<String> = []
    private var replacedParts: [ReplacedPart] = []
    private var photoImages: [UIImage] = []
    private var isEditingExistingRecord: Bool

    // MARK: - Initialization
    init(record: RepairRecord? = nil) {
        self.isEditingExistingRecord = record != nil
        self.repairRecord = record ?? RepairRecord()

        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 80, height: 80)
        layout.minimumInteritemSpacing = 8
        self.photosCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)

        super.init(nibName: nil, bundle: nil)

        if let record = record {
            loadRecordData(record)
        }
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
    }

    private func setupUI() {
        setupGradientBackground()
        setupScrollView()
        setupFormFields()
        setupConstraints()
    }

    private func setupNavigationBar() {
        title = isEditingExistingRecord ? "Edit Repair" : "New Repair"
        navigationController?.navigationBar.tintColor = .white
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.white
        ]

        // Make navigation bar transparent
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true

        let cancelButton = UIBarButtonItem(title: "Cancel", style: .plain, target: self, action: #selector(cancelTapped))
        let saveButton = UIBarButtonItem(title: "Save", style: .done, target: self, action: #selector(saveTapped))

        navigationItem.leftBarButtonItem = cancelButton
        navigationItem.rightBarButtonItem = saveButton
    }

    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        scrollView.keyboardDismissMode = .onDrag
    }

    private func setupFormFields() {
        setupDatePicker()
        setupTextFields()
        setupSymptomsSection()
        setupRepairDescriptionSection()
        setupPartsSection()
        setupPhotosSection()
    }

    private func setupDatePicker() {
        datePicker.datePickerMode = .dateAndTime
        datePicker.preferredDatePickerStyle = .compact
        datePicker.date = repairRecord.repairDate
        datePicker.backgroundColor = .cardBackground
        datePicker.layer.cornerRadius = 8
        contentView.addSubview(datePicker)
    }

    private func setupTextFields() {
        // Brand TextField
        brandTextField.placeholder = "TV Brand (e.g., Samsung, LG, Sony)"
        brandTextField.text = repairRecord.tvBrand
        brandTextField.borderStyle = .roundedRect
        brandTextField.backgroundColor = .cardBackground
        contentView.addSubview(brandTextField)

        // Model TextField
        modelTextField.placeholder = "TV Model (e.g., UN55TU8000)"
        modelTextField.text = repairRecord.tvModel
        modelTextField.borderStyle = .roundedRect
        modelTextField.backgroundColor = .cardBackground
        contentView.addSubview(modelTextField)
    }

    private func setupSymptomsSection() {
        symptomsStackView.axis = .vertical
        symptomsStackView.spacing = 8
        symptomsStackView.backgroundColor = .cardBackground
        symptomsStackView.layer.cornerRadius = 8
        symptomsStackView.layoutMargins = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        symptomsStackView.isLayoutMarginsRelativeArrangement = true

        // Add predefined symptoms as checkboxes
        for symptom in RepairRecord.predefinedFaultSymptoms {
            let button = createSymptomButton(title: symptom)
            symptomsStackView.addArrangedSubview(button)
        }

        // Custom symptom input
        customSymptomTextField.placeholder = "Add custom symptom..."
        customSymptomTextField.borderStyle = .roundedRect
        customSymptomTextField.backgroundColor = .secondaryCardBackground
        customSymptomTextField.addTarget(self, action: #selector(customSymptomChanged), for: .editingChanged)
        symptomsStackView.addArrangedSubview(customSymptomTextField)

        contentView.addSubview(symptomsStackView)

        // Load existing symptoms
        selectedSymptoms = Set(repairRecord.faultSymptoms)
        updateSymptomButtons()
    }

    private func setupRepairDescriptionSection() {
        repairDescriptionTextView.text = repairRecord.repairDescription
        repairDescriptionTextView.font = .systemFont(ofSize: 16)
        repairDescriptionTextView.backgroundColor = .cardBackground
        repairDescriptionTextView.layer.cornerRadius = 8
        repairDescriptionTextView.layer.borderColor = UIColor.separator.cgColor
        repairDescriptionTextView.layer.borderWidth = 0.5
        contentView.addSubview(repairDescriptionTextView)
    }

    private func setupPartsSection() {
        partsStackView.axis = .vertical
        partsStackView.spacing = 8
        partsStackView.backgroundColor = .cardBackground
        partsStackView.layer.cornerRadius = 8
        partsStackView.layoutMargins = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        partsStackView.isLayoutMarginsRelativeArrangement = true

        // Add parts button
        let addPartButton = UIButton(type: .system)
        addPartButton.setTitle("+ Add Replaced Part", for: .normal)
        addPartButton.setImage(UIImage(systemName: "plus.circle"), for: .normal)
        addPartButton.backgroundColor = .systemBlue
        addPartButton.setTitleColor(.white, for: .normal)
        addPartButton.tintColor = .white
        addPartButton.layer.cornerRadius = 8
        addPartButton.contentEdgeInsets = UIEdgeInsets(top: 8, left: 12, bottom: 8, right: 12)
        addPartButton.addTarget(self, action: #selector(addPartTapped), for: .touchUpInside)
        partsStackView.addArrangedSubview(addPartButton)

        contentView.addSubview(partsStackView)

        // Load existing parts
        replacedParts = repairRecord.replacedParts
        updatePartsDisplay()
    }

    private func setupPhotosSection() {
        photosCollectionView.backgroundColor = .cardBackground
        photosCollectionView.layer.cornerRadius = 8
        photosCollectionView.delegate = self
        photosCollectionView.dataSource = self
        photosCollectionView.register(PhotoCell.self, forCellWithReuseIdentifier: "PhotoCell")
        photosCollectionView.register(AddPhotoCell.self, forCellWithReuseIdentifier: "AddPhotoCell")
        contentView.addSubview(photosCollectionView)

        // Load existing photos
        loadPhotosFromData()
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        let dateLabel = createSectionLabel(text: "Repair Date & Time")
        contentView.addSubview(dateLabel)

        let brandLabel = createSectionLabel(text: "TV Brand")
        contentView.addSubview(brandLabel)

        let modelLabel = createSectionLabel(text: "TV Model")
        contentView.addSubview(modelLabel)

        let symptomsLabel = createSectionLabel(text: "Fault Symptoms")
        contentView.addSubview(symptomsLabel)

        let descriptionLabel = createSectionLabel(text: "Repair Description")
        contentView.addSubview(descriptionLabel)

        let partsLabel = createSectionLabel(text: "Replaced Parts")
        contentView.addSubview(partsLabel)

        let photosLabel = createSectionLabel(text: "Photos")
        contentView.addSubview(photosLabel)

        // Layout constraints
        dateLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        datePicker.snp.makeConstraints { make in
            make.top.equalTo(dateLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        brandLabel.snp.makeConstraints { make in
            make.top.equalTo(datePicker.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        brandTextField.snp.makeConstraints { make in
            make.top.equalTo(brandLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        modelLabel.snp.makeConstraints { make in
            make.top.equalTo(brandTextField.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        modelTextField.snp.makeConstraints { make in
            make.top.equalTo(modelLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        symptomsLabel.snp.makeConstraints { make in
            make.top.equalTo(modelTextField.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        symptomsStackView.snp.makeConstraints { make in
            make.top.equalTo(symptomsLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(symptomsStackView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        repairDescriptionTextView.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(100)
        }

        partsLabel.snp.makeConstraints { make in
            make.top.equalTo(repairDescriptionTextView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        partsStackView.snp.makeConstraints { make in
            make.top.equalTo(partsLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        photosLabel.snp.makeConstraints { make in
            make.top.equalTo(partsStackView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        photosCollectionView.snp.makeConstraints { make in
            make.top.equalTo(photosLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(100)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // MARK: - Helper Methods
    private func createSectionLabel(text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .white
        return label
    }

    private func createSymptomButton(title: String) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setImage(UIImage(systemName: "square"), for: .normal)
        button.setImage(UIImage(systemName: "checkmark.square.fill"), for: .selected)
        button.tintColor = .systemBlue
        button.contentHorizontalAlignment = .leading
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 0)
        button.addTarget(self, action: #selector(symptomButtonTapped(_:)), for: .touchUpInside)
        return button
    }

    private func updateSymptomButtons() {
        for case let button as UIButton in symptomsStackView.arrangedSubviews {
            if let title = button.title(for: .normal), title != "Add custom symptom..." {
                button.isSelected = selectedSymptoms.contains(title)
            }
        }
    }

    private func updatePartsDisplay() {
        // Remove existing part views (except the add button)
        let addButton = partsStackView.arrangedSubviews.first
        partsStackView.arrangedSubviews.forEach { view in
            if view != addButton {
                partsStackView.removeArrangedSubview(view)
                view.removeFromSuperview()
            }
        }

        // Add part views
        for (index, part) in replacedParts.enumerated() {
            let partView = createPartView(part: part, index: index)
            partsStackView.insertArrangedSubview(partView, at: partsStackView.arrangedSubviews.count - 1)
        }
    }

    private func createPartView(part: ReplacedPart, index: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .secondaryCardBackground
        containerView.layer.cornerRadius = 8

        let nameLabel = UILabel()
        nameLabel.text = part.partName
        nameLabel.font = .systemFont(ofSize: 16, weight: .medium)

        let quantityLabel = UILabel()
        quantityLabel.text = "Qty: \(part.quantity)"
        quantityLabel.font = .systemFont(ofSize: 14)
        quantityLabel.textColor = .secondaryLabel

        let customerLabel = UILabel()
        customerLabel.text = part.isCustomerProvided ? "Customer Provided" : "Shop Provided"
        customerLabel.font = .systemFont(ofSize: 12)
        customerLabel.textColor = part.isCustomerProvided ? .systemOrange : .systemGreen

        let deleteButton = UIButton(type: .system)
        deleteButton.setImage(UIImage(systemName: "trash"), for: .normal)
        deleteButton.tintColor = .systemRed
        deleteButton.tag = index
        deleteButton.addTarget(self, action: #selector(deletePartTapped(_:)), for: .touchUpInside)

        containerView.addSubview(nameLabel)
        containerView.addSubview(quantityLabel)
        containerView.addSubview(customerLabel)
        containerView.addSubview(deleteButton)

        nameLabel.snp.makeConstraints { make in
            make.leading.top.equalToSuperview().offset(12)
            make.trailing.equalTo(deleteButton.snp.leading).offset(-8)
        }

        quantityLabel.snp.makeConstraints { make in
            make.leading.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
        }

        customerLabel.snp.makeConstraints { make in
            make.leading.equalTo(nameLabel)
            make.top.equalTo(quantityLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().offset(-12)
        }

        deleteButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }

        return containerView
    }

    private func loadRecordData(_ record: RepairRecord) {
        datePicker.date = record.repairDate
        brandTextField.text = record.tvBrand
        modelTextField.text = record.tvModel
        selectedSymptoms = Set(record.faultSymptoms)
        repairDescriptionTextView.text = record.repairDescription
        replacedParts = record.replacedParts
    }

    private func loadPhotosFromData() {
        photoImages = repairRecord.photoData.compactMap { UIImage(data: $0) }
        photosCollectionView.reloadData()
    }

    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }

    @objc private func saveTapped() {
        let brand = brandTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let model = modelTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        guard !brand.isEmpty && !model.isEmpty else {
            showAlert(title: "Missing Information", message: "Please enter both TV brand and model.")
            return
        }

        // Update record with form data
        repairRecord.repairDate = datePicker.date
        repairRecord.tvBrand = brand
        repairRecord.tvModel = model
        repairRecord.faultSymptoms = Array(selectedSymptoms)
        repairRecord.repairDescription = repairDescriptionTextView.text ?? ""
        repairRecord.replacedParts = replacedParts
        repairRecord.photoData = photoImages.compactMap { $0.jpegData(compressionQuality: 0.8) }

        RepairRecordManager.shared.saveRepairRecord(repairRecord)
        dismiss(animated: true)
    }

    @objc private func symptomButtonTapped(_ sender: UIButton) {
        guard let title = sender.title(for: .normal) else { return }

        if selectedSymptoms.contains(title) {
            selectedSymptoms.remove(title)
        } else {
            selectedSymptoms.insert(title)
        }

        sender.isSelected = selectedSymptoms.contains(title)
    }

    @objc private func customSymptomChanged() {
        guard let text = customSymptomTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
              !text.isEmpty else { return }

        if !selectedSymptoms.contains(text) {
            selectedSymptoms.insert(text)

            // Add new button for this symptom
            let button = createSymptomButton(title: text)
            button.isSelected = true
            symptomsStackView.insertArrangedSubview(button, at: symptomsStackView.arrangedSubviews.count - 1)

            customSymptomTextField.text = ""
        }
    }

    @objc private func addPartTapped() {
        let alert = UIAlertController(title: "Add Replaced Part", message: nil, preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "Part name"
        }

        alert.addTextField { textField in
            textField.placeholder = "Quantity"
            textField.keyboardType = .numberPad
        }

        let addAction = UIAlertAction(title: "Add", style: .default) { _ in
            guard let partName = alert.textFields?[0].text?.trimmingCharacters(in: .whitespacesAndNewlines),
                  !partName.isEmpty,
                  let quantityText = alert.textFields?[1].text,
                  let quantity = Int(quantityText), quantity > 0 else {
                self.showAlert(title: "Invalid Input", message: "Please enter valid part name and quantity.")
                return
            }

            let customerProvidedAlert = UIAlertController(title: "Part Source", message: "Who provided this part?", preferredStyle: .alert)

            customerProvidedAlert.addAction(UIAlertAction(title: "Shop", style: .default) { _ in
                let part = ReplacedPart(partName: partName, quantity: quantity, isCustomerProvided: false)
                self.replacedParts.append(part)
                self.updatePartsDisplay()
            })

            customerProvidedAlert.addAction(UIAlertAction(title: "Customer", style: .default) { _ in
                let part = ReplacedPart(partName: partName, quantity: quantity, isCustomerProvided: true)
                self.replacedParts.append(part)
                self.updatePartsDisplay()
            })

            self.present(customerProvidedAlert, animated: true)
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(addAction)

        present(alert, animated: true)
    }

    @objc private func deletePartTapped(_ sender: UIButton) {
        let index = sender.tag
        guard index < replacedParts.count else { return }

        replacedParts.remove(at: index)
        updatePartsDisplay()
    }
}

// MARK: - UICollectionViewDataSource
extension RepairRecordDetailViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return photoImages.count + 1 // +1 for add photo cell
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.item < photoImages.count {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PhotoCell", for: indexPath) as! PhotoCell
            cell.configure(with: photoImages[indexPath.item])
            return cell
        } else {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "AddPhotoCell", for: indexPath) as! AddPhotoCell
            return cell
        }
    }
}

// MARK: - UICollectionViewDelegate
extension RepairRecordDetailViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item < photoImages.count {
            // Show photo detail or delete option
            let alert = UIAlertController(title: "Photo Options", message: nil, preferredStyle: .actionSheet)
            alert.addAction(UIAlertAction(title: "Delete Photo", style: .destructive) { _ in
                self.photoImages.remove(at: indexPath.item)
                collectionView.reloadData()
            })
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

            if let popover = alert.popoverPresentationController {
                popover.sourceView = collectionView.cellForItem(at: indexPath)
                popover.sourceRect = collectionView.cellForItem(at: indexPath)?.bounds ?? .zero
            }

            present(alert, animated: true)
        } else {
            // Add photo
            showImagePicker()
        }
    }

    private func showImagePicker() {
        let alert = UIAlertController(title: "Add Photo", message: "Choose photo source", preferredStyle: .actionSheet)

        if UIImagePickerController.isSourceTypeAvailable(.camera) {
            alert.addAction(UIAlertAction(title: "Camera", style: .default) { _ in
                self.presentImagePicker(sourceType: .camera)
            })
        }

        alert.addAction(UIAlertAction(title: "Photo Library", style: .default) { _ in
            self.presentImagePicker(sourceType: .photoLibrary)
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }

        present(alert, animated: true)
    }

    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = self
        picker.allowsEditing = true
        present(picker, animated: true)
    }
}

// MARK: - UIImagePickerControllerDelegate
extension RepairRecordDetailViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        if let editedImage = info[.editedImage] as? UIImage {
            photoImages.append(editedImage)
        } else if let originalImage = info[.originalImage] as? UIImage {
            photoImages.append(originalImage)
        }

        photosCollectionView.reloadData()
        picker.dismiss(animated: true)
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}
