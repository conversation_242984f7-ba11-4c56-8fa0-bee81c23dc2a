//
//  NotificationManager.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import Foundation
import UserNotifications

class NotificationManager {
    static let shared = NotificationManager()

    private init() {
        requestNotificationPermission()
    }

    func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("Notification permission granted")
            } else if let error = error {
                print("Notification permission error: \(error.localizedDescription)")
            }
        }
    }

    func scheduleNotification(for schedule: RepairSchedule) {
        // Cancel existing notification if any
        cancelNotification(for: schedule)

        let content = UNMutableNotificationContent()
        content.title = "维修提醒"
        content.body = "即将前往 \(schedule.customerName) 处维修电视"
        content.sound = .default
        content.badge = 1

        // Add custom data
        content.userInfo = [
            "scheduleId": schedule.id,
            "customerName": schedule.customerName,
            "address": schedule.address
        ]

        // Calculate trigger date
        let triggerDate = schedule.scheduledDate.addingTimeInterval(-schedule.reminderTime.timeInterval)

        // Only schedule if the trigger date is in the future
        guard triggerDate > Date() else { return }

        let calendar = Calendar.current
        let dateComponents = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: triggerDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)

        let notificationId = "repair_reminder_\(schedule.id)"
        let request = UNNotificationRequest(identifier: notificationId, content: content, trigger: trigger)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling notification: \(error.localizedDescription)")
            } else {
                print("Notification scheduled for \(schedule.customerName) at \(triggerDate)")
            }
        }
    }

    func cancelNotification(for schedule: RepairSchedule) {
        let notificationId = schedule.notificationId ?? "repair_reminder_\(schedule.id)"
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [notificationId])
    }

    func cancelAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }

    func getPendingNotifications(completion: @escaping ([UNNotificationRequest]) -> Void) {
        UNUserNotificationCenter.current().getPendingNotificationRequests { requests in
            DispatchQueue.main.async {
                completion(requests)
            }
        }
    }
}
