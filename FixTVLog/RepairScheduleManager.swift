//
//  RepairScheduleManager.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import Foundation

class RepairScheduleManager {
    static let shared = RepairScheduleManager()
    private let userDefaults = UserDefaults.standard
    private let schedulesKey = "RepairSchedules"

    private init() {
        // Initialize with default data if no schedules exist
        if getRepairSchedules().isEmpty {
            initializeDefaultSchedules()
        }
    }

    func getRepairSchedules() -> [RepairSchedule] {
        guard let data = userDefaults.data(forKey: schedulesKey),
              let schedules = try? JSONDecoder().decode([RepairSchedule].self, from: data) else {
            return []
        }
        return schedules.sorted { $0.scheduledDate < $1.scheduledDate }
    }

    func saveRepairSchedule(_ schedule: RepairSchedule) {
        var schedules = getRepairSchedules()

        if let index = schedules.firstIndex(where: { $0.id == schedule.id }) {
            schedules[index] = schedule
        } else {
            schedules.append(schedule)
        }

        saveSchedules(schedules)

        // Schedule notification
        NotificationManager.shared.scheduleNotification(for: schedule)
    }

    func deleteRepairSchedule(withId id: String) {
        var schedules = getRepairSchedules()

        // Cancel notification before deleting
        if let schedule = schedules.first(where: { $0.id == id }) {
            NotificationManager.shared.cancelNotification(for: schedule)
        }

        schedules.removeAll { $0.id == id }
        saveSchedules(schedules)
    }

    func getSchedulesForDate(_ date: Date) -> [RepairSchedule] {
        let calendar = Calendar.current
        return getRepairSchedules().filter { schedule in
            calendar.isDate(schedule.scheduledDate, inSameDayAs: date)
        }
    }

    func getSchedulesForWeek(containing date: Date) -> [RepairSchedule] {
        let calendar = Calendar.current
        guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: date) else {
            return []
        }

        return getRepairSchedules().filter { schedule in
            weekInterval.contains(schedule.scheduledDate)
        }
    }

    func getTodaySchedules() -> [RepairSchedule] {
        return getSchedulesForDate(Date())
    }

    func getUpcomingSchedules(limit: Int = 5) -> [RepairSchedule] {
        let now = Date()
        return getRepairSchedules()
            .filter { $0.scheduledDate >= now && $0.status != .completed && $0.status != .cancelled }
            .prefix(limit)
            .map { $0 }
    }

    private func saveSchedules(_ schedules: [RepairSchedule]) {
        if let data = try? JSONEncoder().encode(schedules) {
            userDefaults.set(data, forKey: schedulesKey)
        }
    }

    private func initializeDefaultSchedules() {
        let calendar = Calendar.current

        // Today's schedule
        let schedule1 = RepairSchedule(
            customerName: "John Smith",
            scheduledDate: calendar.date(byAdding: .hour, value: 2, to: Date()) ?? Date(),
            address: "123 Main Street, New York, NY 10001",
            contactPhone: "555-0123",
            problemDescription: "TV won't turn on, no power indicator light",
            status: .pending,
            reminderTime: .thirtyMinutes
        )

        // Tomorrow's schedule
        let schedule2 = RepairSchedule(
            customerName: "Sarah Johnson",
            scheduledDate: calendar.date(byAdding: .day, value: 1, to: Date()) ?? Date(),
            address: "456 Oak Avenue, Los Angeles, CA 90210",
            contactPhone: "555-0456",
            problemDescription: "Screen has vertical lines, color display abnormal",
            status: .pending,
            reminderTime: .oneHour
        )

        // Day after tomorrow's schedule
        let schedule3 = RepairSchedule(
            customerName: "Michael Brown",
            scheduledDate: calendar.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
            address: "789 Pine Road, Chicago, IL 60601",
            contactPhone: "555-0789",
            problemDescription: "HDMI port no signal, need to replace mainboard",
            status: .pending,
            reminderTime: .tenMinutes
        )

        saveSchedules([schedule1, schedule2, schedule3])
    }
}
