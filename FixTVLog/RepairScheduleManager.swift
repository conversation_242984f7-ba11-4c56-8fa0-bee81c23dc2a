//
//  RepairScheduleManager.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import Foundation

class RepairScheduleManager {
    static let shared = RepairScheduleManager()
    private let userDefaults = UserDefaults.standard
    private let schedulesKey = "RepairSchedules"
    
    private init() {
        // Initialize with default data if no schedules exist
        if getRepairSchedules().isEmpty {
            initializeDefaultSchedules()
        }
    }
    
    func getRepairSchedules() -> [RepairSchedule] {
        guard let data = userDefaults.data(forKey: schedulesKey),
              let schedules = try? JSONDecoder().decode([RepairSchedule].self, from: data) else {
            return []
        }
        return schedules.sorted { $0.scheduledDate < $1.scheduledDate }
    }
    
    func saveRepairSchedule(_ schedule: RepairSchedule) {
        var schedules = getRepairSchedules()
        
        if let index = schedules.firstIndex(where: { $0.id == schedule.id }) {
            schedules[index] = schedule
        } else {
            schedules.append(schedule)
        }
        
        saveSchedules(schedules)
        
        // Schedule notification
        NotificationManager.shared.scheduleNotification(for: schedule)
    }
    
    func deleteRepairSchedule(withId id: String) {
        var schedules = getRepairSchedules()
        
        // Cancel notification before deleting
        if let schedule = schedules.first(where: { $0.id == id }) {
            NotificationManager.shared.cancelNotification(for: schedule)
        }
        
        schedules.removeAll { $0.id == id }
        saveSchedules(schedules)
    }
    
    func getSchedulesForDate(_ date: Date) -> [RepairSchedule] {
        let calendar = Calendar.current
        return getRepairSchedules().filter { schedule in
            calendar.isDate(schedule.scheduledDate, inSameDayAs: date)
        }
    }
    
    func getSchedulesForWeek(containing date: Date) -> [RepairSchedule] {
        let calendar = Calendar.current
        guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: date) else {
            return []
        }
        
        return getRepairSchedules().filter { schedule in
            weekInterval.contains(schedule.scheduledDate)
        }
    }
    
    func getTodaySchedules() -> [RepairSchedule] {
        return getSchedulesForDate(Date())
    }
    
    func getUpcomingSchedules(limit: Int = 5) -> [RepairSchedule] {
        let now = Date()
        return getRepairSchedules()
            .filter { $0.scheduledDate >= now && $0.status != .completed && $0.status != .cancelled }
            .prefix(limit)
            .map { $0 }
    }
    
    private func saveSchedules(_ schedules: [RepairSchedule]) {
        if let data = try? JSONEncoder().encode(schedules) {
            userDefaults.set(data, forKey: schedulesKey)
        }
    }
    
    private func initializeDefaultSchedules() {
        let calendar = Calendar.current
        
        // Today's schedule
        let schedule1 = RepairSchedule(
            customerName: "张先生",
            scheduledDate: calendar.date(byAdding: .hour, value: 2, to: Date()) ?? Date(),
            address: "北京市朝阳区建国路88号",
            contactPhone: "138-0000-1234",
            problemDescription: "电视无法开机，指示灯不亮",
            status: .pending,
            reminderTime: .thirtyMinutes
        )
        
        // Tomorrow's schedule
        let schedule2 = RepairSchedule(
            customerName: "李女士",
            scheduledDate: calendar.date(byAdding: .day, value: 1, to: Date()) ?? Date(),
            address: "上海市浦东新区陆家嘴环路1000号",
            contactPhone: "139-0000-5678",
            problemDescription: "屏幕有竖线，颜色显示异常",
            status: .pending,
            reminderTime: .oneHour
        )
        
        // Day after tomorrow's schedule
        let schedule3 = RepairSchedule(
            customerName: "王先生",
            scheduledDate: calendar.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
            address: "广州市天河区珠江新城花城大道123号",
            contactPhone: "137-0000-9999",
            problemDescription: "HDMI接口无信号，需要更换主板",
            status: .pending,
            reminderTime: .tenMinutes
        )
        
        saveSchedules([schedule1, schedule2, schedule3])
    }
}
