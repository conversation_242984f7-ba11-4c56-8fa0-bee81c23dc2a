//
//  Extensions.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import UIKit

// MARK: - UIView Extensions
extension UIView {
    func addGradientBackground() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.systemBlue.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.frame = bounds
        
        // Remove existing gradient layers
        layer.sublayers?.removeAll { $0 is CAGradientLayer }
        layer.insertSublayer(gradientLayer, at: 0)
    }
    
    override open func layoutSubviews() {
        super.layoutSubviews()
        // Update gradient frame when view bounds change
        if let gradientLayer = layer.sublayers?.first(where: { $0 is CAGradientLayer }) as? CAGradientLayer {
            gradientLayer.frame = bounds
        }
    }
}

// MARK: - UIViewController Extensions
extension UIViewController {
    func setupGradientBackground() {
        view.addGradientBackground()
    }
    
    func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }
}

// MARK: - Date Extensions
extension Date {
    func formattedString() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: self)
    }
    
    func shortDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: self)
    }
}

// MARK: - UIColor Extensions
extension UIColor {
    static let cardBackground = UIColor.systemBackground.withAlphaComponent(0.9)
    static let secondaryCardBackground = UIColor.secondarySystemBackground.withAlphaComponent(0.8)
}

// MARK: - String Extensions
extension String {
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
}
