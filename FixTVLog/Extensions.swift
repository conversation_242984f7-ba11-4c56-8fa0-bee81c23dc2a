//
//  Extensions.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import UIKit

// MARK: - UIView Extensions
extension UIView {
    private static var gradientLayerKey: UInt8 = 0

    func addGradientBackground() {
        // Remove existing gradient layer if any
        removeGradientBackground()

        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.systemBlue.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.frame = bounds

        layer.insertSublayer(gradientLayer, at: 0)

        // Store reference to gradient layer
        objc_setAssociatedObject(self, &UIView.gradientLayerKey, gradientLayer, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
    }

    func removeGradientBackground() {
        if let gradientLayer = objc_getAssociatedObject(self, &UIView.gradientLayerKey) as? CAGradientLayer {
            gradientLayer.removeFromSuperlayer()
            objc_setAssociatedObject(self, &UIView.gradientLayerKey, nil, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }

    func updateGradientFrame() {
        if let gradientLayer = objc_getAssociatedObject(self, &UIView.gradientLayerKey) as? CAGradientLayer {
            gradientLayer.frame = bounds
        }
    }
}

// MARK: - UIViewController Extensions
extension UIViewController {
    func setupGradientBackground() {
        let gradientView = GradientView()
        view.insertSubview(gradientView, at: 0)
        gradientView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            gradientView.topAnchor.constraint(equalTo: view.topAnchor),
            gradientView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            gradientView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            gradientView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }
}

// MARK: - Date Extensions
extension Date {
    func formattedString() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: self)
    }

    func shortDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: self)
    }
}

// MARK: - UIColor Extensions
extension UIColor {
    static let cardBackground = UIColor.systemBackground.withAlphaComponent(0.9)
    static let secondaryCardBackground = UIColor.secondarySystemBackground.withAlphaComponent(0.8)
}

// MARK: - String Extensions
extension String {
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
}
