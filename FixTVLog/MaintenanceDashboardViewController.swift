//
//  MaintenanceDashboardViewController.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import UIKit
import SnapKit

class MaintenanceDashboardViewController: UIViewController {

    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let periodSegmentedControl = UISegmentedControl(items: StatisticsPeriod.allCases.map { $0.rawValue })

    // Summary Cards
    private let summaryStackView = UIStackView()

    // Chart Views
    private let faultTypesChartCard = ChartCardView()
    private let partsUsageChartCard = ChartCardView()
    private let brandDistributionChartCard = ChartCardView()
    private let completionTrendChartCard = ChartCardView()
    private let weeklyDensityChartCard = ChartCardView()

    private var currentPeriod: StatisticsPeriod = .month

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    private func setupUI() {
        setupGradientBackground()
        setupNavigationBar()
        setupScrollView()
        setupPeriodSelector()
        setupSummaryCards()
        setupChartCards()
        setupConstraints()
    }

    private func setupNavigationBar() {
        title = "Maintenance Dashboard"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .white
        navigationController?.navigationBar.largeTitleTextAttributes = [
            .foregroundColor: UIColor.white
        ]
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.white
        ]

        // Make navigation bar transparent
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true

        // Refresh button
        let refreshButton = UIBarButtonItem(
            image: UIImage(systemName: "arrow.clockwise"),
            style: .plain,
            target: self,
            action: #selector(refreshData)
        )
        navigationItem.rightBarButtonItem = refreshButton
    }

    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        scrollView.showsVerticalScrollIndicator = false
    }

    private func setupPeriodSelector() {
        periodSegmentedControl.selectedSegmentIndex = 1 // Default to "This Month"
        periodSegmentedControl.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        periodSegmentedControl.selectedSegmentTintColor = .white
        periodSegmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .normal)
        periodSegmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.systemBlue], for: .selected)
        periodSegmentedControl.addTarget(self, action: #selector(periodChanged), for: .valueChanged)
        contentView.addSubview(periodSegmentedControl)

        currentPeriod = StatisticsPeriod.allCases[1] // This Month
    }

    private func setupSummaryCards() {
        summaryStackView.axis = .vertical
        summaryStackView.spacing = 16
        summaryStackView.distribution = .fillEqually
        contentView.addSubview(summaryStackView)
    }

    private func setupChartCards() {
        contentView.addSubview(faultTypesChartCard)
        contentView.addSubview(partsUsageChartCard)
        contentView.addSubview(brandDistributionChartCard)
        contentView.addSubview(completionTrendChartCard)
        contentView.addSubview(weeklyDensityChartCard)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        periodSegmentedControl.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }

        summaryStackView.snp.makeConstraints { make in
            make.top.equalTo(periodSegmentedControl.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        faultTypesChartCard.snp.makeConstraints { make in
            make.top.equalTo(summaryStackView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }

        partsUsageChartCard.snp.makeConstraints { make in
            make.top.equalTo(faultTypesChartCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }

        brandDistributionChartCard.snp.makeConstraints { make in
            make.top.equalTo(partsUsageChartCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }

        completionTrendChartCard.snp.makeConstraints { make in
            make.top.equalTo(brandDistributionChartCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }

        weeklyDensityChartCard.snp.makeConstraints { make in
            make.top.equalTo(completionTrendChartCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(250)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    private func loadData() {
        loadSummaryData()
        loadChartData()
    }

    private func loadSummaryData() {
        let summary = StatisticsManager.shared.getDashboardSummary(for: currentPeriod)

        // Clear existing cards
        summaryStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Create summary cards in rows
        let row1 = createHorizontalStackView()
        row1.addArrangedSubview(StatisticsCardView.totalRepairsCard(count: summary.totalRepairs))
        row1.addArrangedSubview(StatisticsCardView.pendingTasksCard(count: summary.pendingTasks))

        let row2 = createHorizontalStackView()
        row2.addArrangedSubview(StatisticsCardView.completionRateCard(rate: summary.completionRate))
        row2.addArrangedSubview(StatisticsCardView.weeklyScheduleCard(count: summary.thisWeekScheduled))

        summaryStackView.addArrangedSubview(row1)
        summaryStackView.addArrangedSubview(row2)
    }

    private func createHorizontalStackView() -> UIStackView {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 16
        stackView.distribution = .fillEqually
        return stackView
    }

    private func loadChartData() {
        loadFaultTypesChart()
        loadPartsUsageChart()
        loadBrandDistributionChart()
        loadCompletionTrendChart()
        loadWeeklyDensityChart()
    }

    private func loadFaultTypesChart() {
        let faultStats = StatisticsManager.shared.getFaultTypeStatistics(for: currentPeriod)
        let chartData = faultStats.prefix(8).map { stat in
            ChartDataPoint(label: stat.faultType, value: Double(stat.count))
        }

        if chartData.isEmpty {
            faultTypesChartCard.configurePieChart(
                data: [ChartDataPoint(label: "No Data", value: 1)],
                title: "Fault Types Distribution"
            )
        } else {
            faultTypesChartCard.configurePieChart(data: chartData, title: "Fault Types Distribution")
        }
    }

    private func loadPartsUsageChart() {
        let partsStats = StatisticsManager.shared.getPartsUsageStatistics(for: currentPeriod)
        let chartData = partsStats.prefix(10).map { stat in
            ChartDataPoint(label: stat.partName, value: Double(stat.totalUsed))
        }

        if chartData.isEmpty {
            partsUsageChartCard.configureColumnChart(
                data: [ChartDataPoint(label: "No Data", value: 0)],
                title: "Parts Usage Statistics",
                yAxisTitle: "Quantity Used"
            )
        } else {
            partsUsageChartCard.configureColumnChart(
                data: chartData,
                title: "Parts Usage Statistics",
                yAxisTitle: "Quantity Used"
            )
        }
    }

    private func loadBrandDistributionChart() {
        let brandStats = StatisticsManager.shared.getBrandProblemDistribution(for: currentPeriod)
        let chartData = brandStats.prefix(8).map { brand in
            ChartDataPoint(label: brand.brand, value: Double(brand.totalProblems))
        }

        if chartData.isEmpty {
            brandDistributionChartCard.configureBarChart(
                data: [ChartDataPoint(label: "No Data", value: 0)],
                title: "Brand Problem Distribution",
                yAxisTitle: "Problem Count"
            )
        } else {
            brandDistributionChartCard.configureBarChart(
                data: chartData,
                title: "Brand Problem Distribution",
                yAxisTitle: "Problem Count"
            )
        }
    }

    private func loadCompletionTrendChart() {
        let trends = StatisticsManager.shared.getCompletionTrends(for: currentPeriod)
        let completedData = trends.map { trend in
            TrendDataPoint(date: trend.date, value: Double(trend.completedCount))
        }
        let scheduledData = trends.map { trend in
            TrendDataPoint(date: trend.date, value: Double(trend.scheduledCount))
        }

        if completedData.isEmpty {
            completionTrendChartCard.configureLineChart(
                data: [TrendDataPoint(date: Date(), value: 0)],
                title: "Completion Trends",
                yAxisTitle: "Count"
            )
        } else {
            completionTrendChartCard.configureMultiLineChart(
                completedData: completedData,
                scheduledData: scheduledData,
                title: "Completion Trends"
            )
        }
    }

    private func loadWeeklyDensityChart() {
        let densities = StatisticsManager.shared.getWeeklyScheduleDensity()
        let chartData = densities.map { density in
            TrendDataPoint(date: Date(), value: density.density, category: density.dayOfWeek)
        }

        weeklyDensityChartCard.configureAreaChart(
            data: chartData,
            title: "Weekly Schedule Density",
            yAxisTitle: "Density"
        )
    }

    // MARK: - Actions
    @objc private func refreshData() {
        loadData()
    }

    @objc private func periodChanged() {
        currentPeriod = StatisticsPeriod.allCases[periodSegmentedControl.selectedSegmentIndex]
        loadData()
    }
}
