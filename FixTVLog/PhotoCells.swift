//
//  PhotoCells.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import UIKit
import SnapKit

// MARK: - PhotoCell
class PhotoCell: UICollectionViewCell {
    private let imageView = UIImageView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .secondaryCardBackground
        layer.cornerRadius = 8
        clipsToBounds = true
        
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        contentView.addSubview(imageView)
        
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func configure(with image: UIImage) {
        imageView.image = image
    }
}

// MARK: - AddPhotoCell
class AddPhotoCell: UICollectionViewCell {
    private let iconImageView = UIImageView()
    private let label = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .secondaryCardBackground
        layer.cornerRadius = 8
        layer.borderWidth = 2
        layer.borderColor = UIColor.systemBlue.cgColor
        layer.dashPattern = [5, 5]
        
        iconImageView.image = UIImage(systemName: "plus")
        iconImageView.tintColor = .systemBlue
        iconImageView.contentMode = .scaleAspectFit
        contentView.addSubview(iconImageView)
        
        label.text = "Add Photo"
        label.font = .systemFont(ofSize: 10, weight: .medium)
        label.textColor = .systemBlue
        label.textAlignment = .center
        contentView.addSubview(label)
        
        iconImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-8)
            make.width.height.equalTo(24)
        }
        
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(iconImageView.snp.bottom).offset(4)
        }
    }
}

// MARK: - CALayer Extension for Dashed Border
extension CALayer {
    var dashPattern: [NSNumber]? {
        get {
            return nil
        }
        set {
            if let pattern = newValue {
                let dashLayer = CAShapeLayer()
                dashLayer.strokeColor = borderColor
                dashLayer.lineWidth = borderWidth
                dashLayer.lineDashPattern = pattern
                dashLayer.fillColor = UIColor.clear.cgColor
                dashLayer.path = UIBezierPath(roundedRect: bounds, cornerRadius: cornerRadius).cgPath
                dashLayer.frame = bounds
                addSublayer(dashLayer)
            }
        }
    }
}
