//
//  RepairSchedule.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import Foundation
import UIKit

enum ScheduleStatus: String, CaseIterable, Codable {
    case pending = "待上门"
    case inProgress = "已开始"
    case completed = "完成"
    case cancelled = "取消"

    var color: UIColor {
        switch self {
        case .pending:
            return .systemOrange
        case .inProgress:
            return .systemBlue
        case .completed:
            return .systemGreen
        case .cancelled:
            return .systemRed
        }
    }

    var icon: String {
        switch self {
        case .pending:
            return "clock"
        case .inProgress:
            return "wrench.and.screwdriver"
        case .completed:
            return "checkmark.circle"
        case .cancelled:
            return "xmark.circle"
        }
    }
}

enum ReminderTime: Int, CaseIterable, Codable {
    case tenMinutes = 10
    case thirtyMinutes = 30
    case oneHour = 60

    var displayText: String {
        switch self {
        case .tenMinutes:
            return "10分钟前"
        case .thirtyMinutes:
            return "30分钟前"
        case .oneHour:
            return "1小时前"
        }
    }

    var timeInterval: TimeInterval {
        return TimeInterval(self.rawValue * 60)
    }
}

struct RepairSchedule: Codable {
    let id: String
    var customerName: String
    var scheduledDate: Date
    var address: String
    var contactPhone: String
    var problemDescription: String
    var status: ScheduleStatus
    var reminderTime: ReminderTime
    var notificationId: String?
    var linkedRecordId: String? // Link to RepairRecord if created

    init(id: String = UUID().uuidString,
         customerName: String = "",
         scheduledDate: Date = Date(),
         address: String = "",
         contactPhone: String = "",
         problemDescription: String = "",
         status: ScheduleStatus = .pending,
         reminderTime: ReminderTime = .thirtyMinutes,
         notificationId: String? = nil,
         linkedRecordId: String? = nil) {
        self.id = id
        self.customerName = customerName
        self.scheduledDate = scheduledDate
        self.address = address
        self.contactPhone = contactPhone
        self.problemDescription = problemDescription
        self.status = status
        self.reminderTime = reminderTime
        self.notificationId = notificationId
        self.linkedRecordId = linkedRecordId
    }
}

// MARK: - Extensions
extension RepairSchedule {
    var isToday: Bool {
        return Calendar.current.isDateInToday(scheduledDate)
    }

    var isThisWeek: Bool {
        let calendar = Calendar.current
        let now = Date()
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        let endOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.end ?? now
        return scheduledDate >= startOfWeek && scheduledDate <= endOfWeek
    }

    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: scheduledDate)
    }

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: scheduledDate)
    }

    var formattedDateTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: scheduledDate)
    }
}
