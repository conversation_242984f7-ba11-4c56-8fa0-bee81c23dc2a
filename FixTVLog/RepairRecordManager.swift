//
//  RepairRecordManager.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import Foundation

class RepairRecordManager {
    static let shared = RepairRecordManager()
    private let userDefaults = UserDefaults.standard
    private let recordsKey = "RepairRecords"

    private init() {
        // Initialize with default data if no records exist
        if getRepairRecords().isEmpty {
            initializeDefaultRecords()
        }
    }

    func getRepairRecords() -> [RepairRecord] {
        guard let data = userDefaults.data(forKey: recordsKey),
              let records = try? JSONDecoder().decode([RepairRecord].self, from: data) else {
            return []
        }
        return records.sorted { $0.repairDate > $1.repairDate }
    }

    func saveRepairRecord(_ record: RepairRecord) {
        var records = getRepairRecords()

        if let index = records.firstIndex(where: { $0.id == record.id }) {
            records[index] = record
        } else {
            records.append(record)
        }

        saveRecords(records)

        // Post notification for UI updates
        NotificationCenter.default.post(name: NSNotification.Name("RecordUpdated"), object: nil)
    }

    func deleteRepairRecord(withId id: String) {
        var records = getRepairRecords()
        records.removeAll { $0.id == id }
        saveRecords(records)

        // Post notification for UI updates
        NotificationCenter.default.post(name: NSNotification.Name("RecordUpdated"), object: nil)
    }

    private func saveRecords(_ records: [RepairRecord]) {
        if let data = try? JSONEncoder().encode(records) {
            userDefaults.set(data, forKey: recordsKey)
        }
    }

    private func initializeDefaultRecords() {
        let calendar = Calendar.current

        let record1 = RepairRecord(
            repairDate: calendar.date(byAdding: .day, value: -7, to: Date()) ?? Date(),
            tvBrand: "Samsung",
            tvModel: "UN55TU8000",
            faultSymptoms: ["No Signal", "HDMI Port Issues"],
            repairDescription: "Replaced faulty HDMI board. Customer reported intermittent signal loss on HDMI 1 port. After diagnosis, found damaged HDMI connector. Replaced entire HDMI board and tested all ports successfully.",
            replacedParts: [
                ReplacedPart(partName: "HDMI Board", quantity: 1, isCustomerProvided: false),
                ReplacedPart(partName: "HDMI Cable", quantity: 1, isCustomerProvided: true)
            ]
        )

        let record2 = RepairRecord(
            repairDate: calendar.date(byAdding: .day, value: -3, to: Date()) ?? Date(),
            tvBrand: "LG",
            tvModel: "OLED55C1PUB",
            faultSymptoms: ["Screen Flickering", "Color Issues"],
            repairDescription: "Diagnosed and repaired T-CON board issue causing color distortion and flickering. Updated firmware to latest version. Performed full calibration and color adjustment.",
            replacedParts: [
                ReplacedPart(partName: "T-CON Board", quantity: 1, isCustomerProvided: false)
            ]
        )

        let record3 = RepairRecord(
            repairDate: calendar.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
            tvBrand: "Sony",
            tvModel: "XBR65X900H",
            faultSymptoms: ["Cannot Power On", "No Sound"],
            repairDescription: "Power supply unit failure. Replaced main power board and performed safety checks. Also replaced audio amplifier IC that was damaged due to power surge. TV now functioning normally.",
            replacedParts: [
                ReplacedPart(partName: "Power Supply Board", quantity: 1, isCustomerProvided: false),
                ReplacedPart(partName: "Audio Amplifier IC", quantity: 1, isCustomerProvided: false),
                ReplacedPart(partName: "Fuse 5A", quantity: 2, isCustomerProvided: false)
            ]
        )

        saveRecords([record1, record2, record3])
    }
}
