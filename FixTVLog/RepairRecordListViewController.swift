//
//  RepairRecordListViewController.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import UIKit
import SnapKit

class RepairRecordListViewController: UIViewController {

    private let tableView = UITableView()
    private var repairRecords: [RepairRecord] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    private func setupUI() {
        setupGradientBackground()
        setupNavigationBar()
        setupTableView()
        setupConstraints()
    }

    private func setupNavigationBar() {
        title = "Repair Records"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .white
        navigationController?.navigationBar.largeTitleTextAttributes = [
            .foregroundColor: UIColor.white
        ]
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.white
        ]

        // Make navigation bar transparent
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true

        // Add button
        let addButton = UIBarButtonItem(
            image: UIImage(systemName: "plus"),
            style: .plain,
            target: self,
            action: #selector(addButtonTapped)
        )
        navigationItem.rightBarButtonItem = addButton
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.register(RepairRecordTableViewCell.self, forCellReuseIdentifier: RepairRecordTableViewCell.identifier)
        tableView.contentInsetAdjustmentBehavior = .automatic
        view.addSubview(tableView)
    }

    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }

    private func loadData() {
        repairRecords = RepairRecordManager.shared.getRepairRecords()
        tableView.reloadData()
    }

    @objc private func addButtonTapped() {
        let detailVC = RepairRecordDetailViewController()
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension RepairRecordListViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return repairRecords.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: RepairRecordTableViewCell.identifier, for: indexPath) as? RepairRecordTableViewCell else {
            return UITableViewCell()
        }

        cell.configure(with: repairRecords[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension RepairRecordListViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let record = repairRecords[indexPath.row]
        let detailVC = RepairRecordDetailViewController(record: record)
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let record = repairRecords[indexPath.row]

            let alert = UIAlertController(title: "Delete Record", message: "Are you sure you want to delete this repair record?", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                RepairRecordManager.shared.deleteRepairRecord(withId: record.id)
                self.loadData()
            })

            present(alert, animated: true)
        }
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }
}
