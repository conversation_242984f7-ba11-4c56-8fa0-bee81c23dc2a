//
//  RepairSchedulerViewController.swift
//  FixTVLog
//
//  Created by tyu<PERSON> on 2025/5/26.
//

import UIKit
import SnapKit

class RepairSchedulerViewController: UIViewController {

    private let segmentedControl = UISegmentedControl(items: ["Week", "Month", "Year"])
    private let tableView = UITableView()
    private var repairSchedules: [RepairSchedule] = []
    private var filteredSchedules: [RepairSchedule] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()

        // Listen for schedule updates
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(scheduleUpdated),
            name: NSNotification.Name("ScheduleUpdated"),
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    private func setupUI() {
        setupGradientBackground()
        setupNavigationBar()
        setupSegmentedControl()
        setupTableView()
        setupConstraints()
    }

    private func setupNavigationBar() {
        title = "Repair Schedule"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .white
        navigationController?.navigationBar.largeTitleTextAttributes = [
            .foregroundColor: UIColor.white
        ]
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.white
        ]

        // Make navigation bar transparent
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true

        // Add button
        let addButton = UIBarButtonItem(
            image: UIImage(systemName: "plus"),
            style: .plain,
            target: self,
            action: #selector(addButtonTapped)
        )

        // Calendar button
        let calendarButton = UIBarButtonItem(
            image: UIImage(systemName: "calendar"),
            style: .plain,
            target: self,
            action: #selector(calendarButtonTapped)
        )

        navigationItem.rightBarButtonItems = [addButton, calendarButton]
    }

    private func setupSegmentedControl() {
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        segmentedControl.selectedSegmentTintColor = .white
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .normal)
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.systemBlue], for: .selected)
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
        view.addSubview(segmentedControl)
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.register(ScheduleTableViewCell.self, forCellReuseIdentifier: ScheduleTableViewCell.identifier)
        tableView.contentInsetAdjustmentBehavior = .automatic
        view.addSubview(tableView)
    }

    private func setupConstraints() {
        segmentedControl.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }

        tableView.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(20)
            make.leading.trailing.bottom.equalTo(view.safeAreaLayoutGuide)
        }
    }

    private func loadData() {
        repairSchedules = RepairScheduleManager.shared.getRepairSchedules()
        filterSchedules()
    }

    private func filterSchedules() {
        let calendar = Calendar.current
        let now = Date()

        switch segmentedControl.selectedSegmentIndex {
        case 0: // Week
            filteredSchedules = RepairScheduleManager.shared.getSchedulesForWeek(containing: Date())
        case 1: // Month
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            let endOfMonth = calendar.dateInterval(of: .month, for: now)?.end ?? now
            filteredSchedules = repairSchedules.filter {
                $0.scheduledDate >= startOfMonth && $0.scheduledDate <= endOfMonth
            }
        case 2: // Year
            let startOfYear = calendar.dateInterval(of: .year, for: now)?.start ?? now
            let endOfYear = calendar.dateInterval(of: .year, for: now)?.end ?? now
            filteredSchedules = repairSchedules.filter {
                $0.scheduledDate >= startOfYear && $0.scheduledDate <= endOfYear
            }
        default:
            filteredSchedules = repairSchedules
        }

        tableView.reloadData()
    }

    @objc private func addButtonTapped() {
        let detailVC = ScheduleDetailViewController()
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }

    @objc private func calendarButtonTapped() {
        let calendarVC = ScheduleCalendarViewController()
        let navController = UINavigationController(rootViewController: calendarVC)
        present(navController, animated: true)
    }

    @objc private func segmentChanged() {
        filterSchedules()
    }

    @objc private func scheduleUpdated() {
        loadData()
    }
}

// MARK: - UITableViewDataSource
extension RepairSchedulerViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return filteredSchedules.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: ScheduleTableViewCell.identifier, for: indexPath) as? ScheduleTableViewCell else {
            return UITableViewCell()
        }

        cell.configure(with: filteredSchedules[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension RepairSchedulerViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let schedule = filteredSchedules[indexPath.row]
        let detailVC = ScheduleDetailViewController(schedule: schedule)
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let schedule = filteredSchedules[indexPath.row]

            let alert = UIAlertController(title: "Delete Schedule", message: "Are you sure you want to delete this repair schedule?", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                RepairScheduleManager.shared.deleteRepairSchedule(withId: schedule.id)
                self.loadData()
            })

            present(alert, animated: true)
        }
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 140
    }

    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let schedule = filteredSchedules[indexPath.row]

        // Status change actions
        var actions: [UIContextualAction] = []

        // Create repair record action (only for completed schedules)
        if schedule.status == .completed && schedule.linkedRecordId == nil {
            let createRecordAction = UIContextualAction(style: .normal, title: "Create Record") { _, _, completion in
                self.createRepairRecord(for: schedule)
                completion(true)
            }
            createRecordAction.backgroundColor = .systemGreen
            createRecordAction.image = UIImage(systemName: "doc.text")
            actions.append(createRecordAction)
        }

        // Status change action
        let nextStatus = getNextStatus(for: schedule.status)
        if let nextStatus = nextStatus {
            let statusAction = UIContextualAction(style: .normal, title: nextStatus.rawValue) { _, _, completion in
                self.updateScheduleStatus(schedule, to: nextStatus)
                completion(true)
            }
            statusAction.backgroundColor = nextStatus.color
            statusAction.image = UIImage(systemName: nextStatus.icon)
            actions.append(statusAction)
        }

        return UISwipeActionsConfiguration(actions: actions)
    }

    private func getNextStatus(for currentStatus: ScheduleStatus) -> ScheduleStatus? {
        switch currentStatus {
        case .pending:
            return .inProgress
        case .inProgress:
            return .completed
        case .completed, .cancelled:
            return nil
        }
    }

    private func updateScheduleStatus(_ schedule: RepairSchedule, to newStatus: ScheduleStatus) {
        var updatedSchedule = schedule
        updatedSchedule.status = newStatus
        RepairScheduleManager.shared.saveRepairSchedule(updatedSchedule)
        loadData()
    }

    private func createRepairRecord(for schedule: RepairSchedule) {
        // Create a new repair record based on the schedule
        let repairRecord = RepairRecord(
            repairDate: schedule.scheduledDate,
            tvBrand: "", // Will be filled in the detail view
            tvModel: "",
            faultSymptoms: [schedule.problemDescription],
            repairDescription: "Repair task from schedule: \(schedule.customerName)",
            replacedParts: [],
            photoData: []
        )

        // Save the repair record
        RepairRecordManager.shared.saveRepairRecord(repairRecord)

        // Link the schedule to the record
        var updatedSchedule = schedule
        updatedSchedule.linkedRecordId = repairRecord.id
        RepairScheduleManager.shared.saveRepairSchedule(updatedSchedule)

        // Show success message
        showAlert(title: "Success", message: "Repair record created successfully. You can view and edit it in the Repair Records section.")

        loadData()
    }
}
