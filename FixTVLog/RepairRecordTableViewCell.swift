//
//  RepairRecordTableViewCell.swift
//  FixTVLog
//
//  Created by tyuu on 2025/5/26.
//

import UIKit
import SnapKit

class RepairRecordTableViewCell: UITableViewCell {
    static let identifier = "RepairRecordTableViewCell"
    
    private let containerView = UIView()
    private let tvIconImageView = UIImageView()
    private let brandModelLabel = UILabel()
    private let dateLabel = UILabel()
    private let symptomsLabel = UILabel()
    private let partsCountLabel = UILabel()
    private let chevronImageView = UIImageView()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // Container view with rounded corners and shadow
        containerView.backgroundColor = .cardBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        containerView.layer.shadowOpacity = 0.1
        contentView.addSubview(containerView)
        
        // TV Icon
        tvIconImageView.image = UIImage(systemName: "tv")
        tvIconImageView.tintColor = .systemBlue
        tvIconImageView.contentMode = .scaleAspectFit
        containerView.addSubview(tvIconImageView)
        
        // Brand and Model Label
        brandModelLabel.font = .systemFont(ofSize: 16, weight: .semibold)
        brandModelLabel.textColor = .label
        brandModelLabel.numberOfLines = 1
        containerView.addSubview(brandModelLabel)
        
        // Date Label
        dateLabel.font = .systemFont(ofSize: 14, weight: .regular)
        dateLabel.textColor = .secondaryLabel
        containerView.addSubview(dateLabel)
        
        // Symptoms Label
        symptomsLabel.font = .systemFont(ofSize: 14, weight: .regular)
        symptomsLabel.textColor = .systemOrange
        symptomsLabel.numberOfLines = 2
        containerView.addSubview(symptomsLabel)
        
        // Parts Count Label
        partsCountLabel.font = .systemFont(ofSize: 12, weight: .medium)
        partsCountLabel.textColor = .systemGreen
        containerView.addSubview(partsCountLabel)
        
        // Chevron
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = .tertiaryLabel
        chevronImageView.contentMode = .scaleAspectFit
        containerView.addSubview(chevronImageView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        tvIconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(24)
        }
        
        brandModelLabel.snp.makeConstraints { make in
            make.leading.equalTo(tvIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().offset(16)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
        }
        
        dateLabel.snp.makeConstraints { make in
            make.leading.equalTo(brandModelLabel)
            make.top.equalTo(brandModelLabel.snp.bottom).offset(4)
            make.trailing.equalTo(brandModelLabel)
        }
        
        symptomsLabel.snp.makeConstraints { make in
            make.leading.equalTo(brandModelLabel)
            make.top.equalTo(dateLabel.snp.bottom).offset(8)
            make.trailing.equalTo(brandModelLabel)
        }
        
        partsCountLabel.snp.makeConstraints { make in
            make.leading.equalTo(brandModelLabel)
            make.top.equalTo(symptomsLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().offset(-16)
            make.trailing.equalTo(brandModelLabel)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    func configure(with record: RepairRecord) {
        brandModelLabel.text = "\(record.tvBrand) \(record.tvModel)"
        dateLabel.text = record.repairDate.shortDateString()
        
        if record.faultSymptoms.isEmpty {
            symptomsLabel.text = "No symptoms recorded"
            symptomsLabel.textColor = .tertiaryLabel
        } else {
            symptomsLabel.text = record.faultSymptoms.joined(separator: ", ")
            symptomsLabel.textColor = .systemOrange
        }
        
        let partsCount = record.replacedParts.count
        if partsCount == 0 {
            partsCountLabel.text = "No parts replaced"
            partsCountLabel.textColor = .tertiaryLabel
        } else {
            partsCountLabel.text = "\(partsCount) part\(partsCount == 1 ? "" : "s") replaced"
            partsCountLabel.textColor = .systemGreen
        }
    }
}
