// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		208AED8420C1C1CEA198C563 /* Pods_FixTVLog.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0199C14CFE276EBCF2061311 /* Pods_FixTVLog.framework */; };
		95C8E3702DE49FC500D76812 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3662DE49FC500D76812 /* AppDelegate.swift */; };
		95C8E3732DE49FC500D76812 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 95C8E3672DE49FC500D76812 /* Assets.xcassets */; };
		95C8E3752DE49FC500D76812 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 95C8E36A2DE49FC500D76812 /* LaunchScreen.storyboard */; };
		95C8E39F2DE4A70A00D76812 /* ScheduleCalendarViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E39A2DE4A70A00D76812 /* ScheduleCalendarViewController.swift */; };
		95C8E3A02DE4A70A00D76812 /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E38A2DE4A70A00D76812 /* Extensions.swift */; };
		95C8E3A12DE4A70A00D76812 /* RepairRecordManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3942DE4A70A00D76812 /* RepairRecordManager.swift */; };
		95C8E3A22DE4A70A00D76812 /* PhotoCells.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3902DE4A70A00D76812 /* PhotoCells.swift */; };
		95C8E3A32DE4A70A00D76812 /* RepairRecordListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3932DE4A70A00D76812 /* RepairRecordListViewController.swift */; };
		95C8E3A42DE4A70A00D76812 /* RepairRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3912DE4A70A00D76812 /* RepairRecord.swift */; };
		95C8E3A52DE4A70A00D76812 /* RepairSchedule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3962DE4A70A00D76812 /* RepairSchedule.swift */; };
		95C8E3A62DE4A70A00D76812 /* ScheduleTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E39C2DE4A70A00D76812 /* ScheduleTableViewCell.swift */; };
		95C8E3A72DE4A70A00D76812 /* RepairSchedulerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3982DE4A70A00D76812 /* RepairSchedulerViewController.swift */; };
		95C8E3A82DE4A70A00D76812 /* StatisticsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E39E2DE4A70A00D76812 /* StatisticsManager.swift */; };
		95C8E3A92DE4A70A00D76812 /* ScheduleDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E39B2DE4A70A00D76812 /* ScheduleDetailViewController.swift */; };
		95C8E3AA2DE4A70A00D76812 /* StatisticsCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E39D2DE4A70A00D76812 /* StatisticsCardView.swift */; };
		95C8E3AB2DE4A70A00D76812 /* RepairRecordTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3952DE4A70A00D76812 /* RepairRecordTableViewCell.swift */; };
		95C8E3AC2DE4A70A00D76812 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3992DE4A70A00D76812 /* SceneDelegate.swift */; };
		95C8E3AD2DE4A70A00D76812 /* ChartCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3892DE4A70A00D76812 /* ChartCardView.swift */; };
		95C8E3AF2DE4A70A00D76812 /* RepairScheduleManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3972DE4A70A00D76812 /* RepairScheduleManager.swift */; };
		95C8E3B02DE4A70A00D76812 /* GradientView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E38C2DE4A70A00D76812 /* GradientView.swift */; };
		95C8E3B12DE4A70A00D76812 /* MaintenanceDashboardViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E38D2DE4A70A00D76812 /* MaintenanceDashboardViewController.swift */; };
		95C8E3B22DE4A70A00D76812 /* MaintenanceStatistics.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E38E2DE4A70A00D76812 /* MaintenanceStatistics.swift */; };
		95C8E3B32DE4A70A00D76812 /* NotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E38F2DE4A70A00D76812 /* NotificationManager.swift */; };
		95C8E3B42DE4A70A00D76812 /* RepairRecordDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C8E3922DE4A70A00D76812 /* RepairRecordDetailViewController.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0199C14CFE276EBCF2061311 /* Pods_FixTVLog.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FixTVLog.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		53C45B3405EA54754F354F24 /* Pods-FixTVLog.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FixTVLog.debug.xcconfig"; path = "Target Support Files/Pods-FixTVLog/Pods-FixTVLog.debug.xcconfig"; sourceTree = "<group>"; };
		9423A680BC94F055131385B5 /* Pods-FixTVLog.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FixTVLog.release.xcconfig"; path = "Target Support Files/Pods-FixTVLog/Pods-FixTVLog.release.xcconfig"; sourceTree = "<group>"; };
		95C8E34E2DE49FBD00D76812 /* FixTVLog.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FixTVLog.app; sourceTree = BUILT_PRODUCTS_DIR; };
		95C8E3662DE49FC500D76812 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		95C8E3672DE49FC500D76812 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		95C8E3682DE49FC500D76812 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		95C8E3692DE49FC500D76812 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		95C8E3892DE4A70A00D76812 /* ChartCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChartCardView.swift; sourceTree = "<group>"; };
		95C8E38A2DE4A70A00D76812 /* Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		95C8E38C2DE4A70A00D76812 /* GradientView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GradientView.swift; sourceTree = "<group>"; };
		95C8E38D2DE4A70A00D76812 /* MaintenanceDashboardViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MaintenanceDashboardViewController.swift; sourceTree = "<group>"; };
		95C8E38E2DE4A70A00D76812 /* MaintenanceStatistics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MaintenanceStatistics.swift; sourceTree = "<group>"; };
		95C8E38F2DE4A70A00D76812 /* NotificationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationManager.swift; sourceTree = "<group>"; };
		95C8E3902DE4A70A00D76812 /* PhotoCells.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoCells.swift; sourceTree = "<group>"; };
		95C8E3912DE4A70A00D76812 /* RepairRecord.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairRecord.swift; sourceTree = "<group>"; };
		95C8E3922DE4A70A00D76812 /* RepairRecordDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairRecordDetailViewController.swift; sourceTree = "<group>"; };
		95C8E3932DE4A70A00D76812 /* RepairRecordListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairRecordListViewController.swift; sourceTree = "<group>"; };
		95C8E3942DE4A70A00D76812 /* RepairRecordManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairRecordManager.swift; sourceTree = "<group>"; };
		95C8E3952DE4A70A00D76812 /* RepairRecordTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairRecordTableViewCell.swift; sourceTree = "<group>"; };
		95C8E3962DE4A70A00D76812 /* RepairSchedule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairSchedule.swift; sourceTree = "<group>"; };
		95C8E3972DE4A70A00D76812 /* RepairScheduleManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairScheduleManager.swift; sourceTree = "<group>"; };
		95C8E3982DE4A70A00D76812 /* RepairSchedulerViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepairSchedulerViewController.swift; sourceTree = "<group>"; };
		95C8E3992DE4A70A00D76812 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		95C8E39A2DE4A70A00D76812 /* ScheduleCalendarViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduleCalendarViewController.swift; sourceTree = "<group>"; };
		95C8E39B2DE4A70A00D76812 /* ScheduleDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduleDetailViewController.swift; sourceTree = "<group>"; };
		95C8E39C2DE4A70A00D76812 /* ScheduleTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduleTableViewCell.swift; sourceTree = "<group>"; };
		95C8E39D2DE4A70A00D76812 /* StatisticsCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatisticsCardView.swift; sourceTree = "<group>"; };
		95C8E39E2DE4A70A00D76812 /* StatisticsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatisticsManager.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		95C8E34B2DE49FBD00D76812 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				208AED8420C1C1CEA198C563 /* Pods_FixTVLog.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		95C8E3452DE49FBD00D76812 = {
			isa = PBXGroup;
			children = (
				95C8E36F2DE49FC500D76812 /* FixTVLog */,
				95C8E34F2DE49FBD00D76812 /* Products */,
				F2CF860311D1849D060039E8 /* Pods */,
				A81ACCCBCF95F2C5A464C2F1 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		95C8E34F2DE49FBD00D76812 /* Products */ = {
			isa = PBXGroup;
			children = (
				95C8E34E2DE49FBD00D76812 /* FixTVLog.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		95C8E36F2DE49FC500D76812 /* FixTVLog */ = {
			isa = PBXGroup;
			children = (
				95C8E3892DE4A70A00D76812 /* ChartCardView.swift */,
				95C8E38A2DE4A70A00D76812 /* Extensions.swift */,
				95C8E38C2DE4A70A00D76812 /* GradientView.swift */,
				95C8E38D2DE4A70A00D76812 /* MaintenanceDashboardViewController.swift */,
				95C8E38E2DE4A70A00D76812 /* MaintenanceStatistics.swift */,
				95C8E38F2DE4A70A00D76812 /* NotificationManager.swift */,
				95C8E3902DE4A70A00D76812 /* PhotoCells.swift */,
				95C8E3912DE4A70A00D76812 /* RepairRecord.swift */,
				95C8E3922DE4A70A00D76812 /* RepairRecordDetailViewController.swift */,
				95C8E3932DE4A70A00D76812 /* RepairRecordListViewController.swift */,
				95C8E3942DE4A70A00D76812 /* RepairRecordManager.swift */,
				95C8E3952DE4A70A00D76812 /* RepairRecordTableViewCell.swift */,
				95C8E3962DE4A70A00D76812 /* RepairSchedule.swift */,
				95C8E3972DE4A70A00D76812 /* RepairScheduleManager.swift */,
				95C8E3982DE4A70A00D76812 /* RepairSchedulerViewController.swift */,
				95C8E3992DE4A70A00D76812 /* SceneDelegate.swift */,
				95C8E39A2DE4A70A00D76812 /* ScheduleCalendarViewController.swift */,
				95C8E39B2DE4A70A00D76812 /* ScheduleDetailViewController.swift */,
				95C8E39C2DE4A70A00D76812 /* ScheduleTableViewCell.swift */,
				95C8E39D2DE4A70A00D76812 /* StatisticsCardView.swift */,
				95C8E39E2DE4A70A00D76812 /* StatisticsManager.swift */,
				95C8E3662DE49FC500D76812 /* AppDelegate.swift */,
				95C8E3672DE49FC500D76812 /* Assets.xcassets */,
				95C8E3682DE49FC500D76812 /* Info.plist */,
				95C8E36A2DE49FC500D76812 /* LaunchScreen.storyboard */,
			);
			path = FixTVLog;
			sourceTree = "<group>";
		};
		A81ACCCBCF95F2C5A464C2F1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0199C14CFE276EBCF2061311 /* Pods_FixTVLog.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F2CF860311D1849D060039E8 /* Pods */ = {
			isa = PBXGroup;
			children = (
				53C45B3405EA54754F354F24 /* Pods-FixTVLog.debug.xcconfig */,
				9423A680BC94F055131385B5 /* Pods-FixTVLog.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		95C8E34D2DE49FBD00D76812 /* FixTVLog */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 95C8E3612DE49FBE00D76812 /* Build configuration list for PBXNativeTarget "FixTVLog" */;
			buildPhases = (
				4840508575D3326F0AAC4DDB /* [CP] Check Pods Manifest.lock */,
				95C8E34A2DE49FBD00D76812 /* Sources */,
				95C8E34B2DE49FBD00D76812 /* Frameworks */,
				95C8E34C2DE49FBD00D76812 /* Resources */,
				5FE28F0F0BEA405711462359 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FixTVLog;
			productName = FixTVLog;
			productReference = 95C8E34E2DE49FBD00D76812 /* FixTVLog.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		95C8E3462DE49FBD00D76812 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					95C8E34D2DE49FBD00D76812 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 95C8E3492DE49FBD00D76812 /* Build configuration list for PBXProject "FixTVLog" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 95C8E3452DE49FBD00D76812;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 95C8E34F2DE49FBD00D76812 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				95C8E34D2DE49FBD00D76812 /* FixTVLog */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		95C8E34C2DE49FBD00D76812 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95C8E3732DE49FC500D76812 /* Assets.xcassets in Resources */,
				95C8E3752DE49FC500D76812 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		4840508575D3326F0AAC4DDB /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FixTVLog-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5FE28F0F0BEA405711462359 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FixTVLog/Pods-FixTVLog-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FixTVLog/Pods-FixTVLog-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FixTVLog/Pods-FixTVLog-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		95C8E34A2DE49FBD00D76812 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95C8E3702DE49FC500D76812 /* AppDelegate.swift in Sources */,
				95C8E39F2DE4A70A00D76812 /* ScheduleCalendarViewController.swift in Sources */,
				95C8E3A02DE4A70A00D76812 /* Extensions.swift in Sources */,
				95C8E3A12DE4A70A00D76812 /* RepairRecordManager.swift in Sources */,
				95C8E3A22DE4A70A00D76812 /* PhotoCells.swift in Sources */,
				95C8E3A32DE4A70A00D76812 /* RepairRecordListViewController.swift in Sources */,
				95C8E3A42DE4A70A00D76812 /* RepairRecord.swift in Sources */,
				95C8E3A52DE4A70A00D76812 /* RepairSchedule.swift in Sources */,
				95C8E3A62DE4A70A00D76812 /* ScheduleTableViewCell.swift in Sources */,
				95C8E3A72DE4A70A00D76812 /* RepairSchedulerViewController.swift in Sources */,
				95C8E3A82DE4A70A00D76812 /* StatisticsManager.swift in Sources */,
				95C8E3A92DE4A70A00D76812 /* ScheduleDetailViewController.swift in Sources */,
				95C8E3AA2DE4A70A00D76812 /* StatisticsCardView.swift in Sources */,
				95C8E3AB2DE4A70A00D76812 /* RepairRecordTableViewCell.swift in Sources */,
				95C8E3AC2DE4A70A00D76812 /* SceneDelegate.swift in Sources */,
				95C8E3AD2DE4A70A00D76812 /* ChartCardView.swift in Sources */,
				95C8E3AF2DE4A70A00D76812 /* RepairScheduleManager.swift in Sources */,
				95C8E3B02DE4A70A00D76812 /* GradientView.swift in Sources */,
				95C8E3B12DE4A70A00D76812 /* MaintenanceDashboardViewController.swift in Sources */,
				95C8E3B22DE4A70A00D76812 /* MaintenanceStatistics.swift in Sources */,
				95C8E3B32DE4A70A00D76812 /* NotificationManager.swift in Sources */,
				95C8E3B42DE4A70A00D76812 /* RepairRecordDetailViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		95C8E36A2DE49FC500D76812 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				95C8E3692DE49FC500D76812 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		95C8E3622DE49FBE00D76812 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 53C45B3405EA54754F354F24 /* Pods-FixTVLog.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FixTVLog/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DiagTrack;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to take photos of TV repair work";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs photo library access to attach repair photos";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yiiguiugiapp.FixTVLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		95C8E3632DE49FBE00D76812 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9423A680BC94F055131385B5 /* Pods-FixTVLog.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = FAZXB7LR43;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FixTVLog/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DiagTrack;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to take photos of TV repair work";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs photo library access to attach repair photos";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.txjfdmmv.dfsa;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = dfs;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		95C8E3642DE49FBE00D76812 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		95C8E3652DE49FBE00D76812 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		95C8E3492DE49FBD00D76812 /* Build configuration list for PBXProject "FixTVLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95C8E3642DE49FBE00D76812 /* Debug */,
				95C8E3652DE49FBE00D76812 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		95C8E3612DE49FBE00D76812 /* Build configuration list for PBXNativeTarget "FixTVLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95C8E3622DE49FBE00D76812 /* Debug */,
				95C8E3632DE49FBE00D76812 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 95C8E3462DE49FBD00D76812 /* Project object */;
}
